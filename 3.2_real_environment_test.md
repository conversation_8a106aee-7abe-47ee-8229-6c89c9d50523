3.2 真实环境测试分析

为了验证改进融合算法在真实环境中的有效性,使用自研陪护机器人进行实验验证。实验中,以PC为主机在VMware Workstation Pro进行配置虚拟机,基于64位Ubuntu18.04操作系统的ROS平台,采用Gmapping算法对实验场地进行地图构建。

实验环境设置在室内办公区域,包含多种类型的静态和动态障碍物,如办公桌、椅子、文件柜等家具以及行走的人员。测试场地面积约为15m×12m,机器人需要在起点(1,1)和终点(14,11)之间规划出安全、高效的导航路径。

为验证算法性能,将本文提出的改进BiRRT*算法与传统RRT、BiRRT、APF-RRT、DD-RRT等经典路径规划算法进行对比分析。实验从路径长度、规划时间、节点数量和路径平滑度四个维度评估算法性能。

3.2.1 路径规划效果对比

图4展示了不同算法在相同环境下的路径规划结果。从图中可以看出:

(1) 传统RRT算法(图4a)由于随机采样的特性,生成的路径存在大量冗余节点和不必要的转折,路径长度较长且不够平滑;

(2) BiRRT算法(图4b)通过双向搜索提高了搜索效率,但路径质量仍有待改善,存在明显的锯齿状轨迹;

(3) 自标偏置RRT算法(图4c)通过增加目标导向性,减少了搜索的盲目性,但在复杂环境中仍容易陷入局部最优;

(4) APF-RRT算法(图4d)结合人工势场的引导作用,路径相对平滑,但在障碍物密集区域容易产生震荡;

(5) DD-RRT算法(图4e)通过动态调整搜索策略,在一定程度上改善了路径质量,但计算复杂度较高;

(6) 本文提出的改进算法(图4f)综合了椭圆采样、势场引导和密度自适应等策略,生成的路径不仅长度最短,而且平滑度最佳,有效避免了不必要的转折和震荡。

3.2.2 性能指标定量分析

表1给出了不同算法在多次实验中的平均性能指标对比。从数据可以看出,本文算法在各项指标上均表现优异:

- 路径长度方面,相比传统RRT算法缩短了36.1%,相比BiRRT算法缩短了28.3%;
- 规划时间方面,虽然略高于简单的RRT算法,但相比其他改进算法仍具有明显优势;
- 节点数量方面,通过椭圆采样和密度自适应策略,有效减少了无效节点的生成;
- 路径平滑度方面,结合三次样条插值优化,显著提升了路径的连续性和可执行性。

3.2.3 真实机器人验证实验

在仿真验证的基础上,进一步在真实陪护机器人平台上进行了实地测试。机器人配置激光雷达、深度相机等传感器,能够实时感知环境信息并进行路径规划。

实验结果表明,改进算法在真实环境中表现稳定,能够有效处理传感器噪声和环境不确定性。机器人按照规划路径成功完成了多次导航任务,路径执行精度高,运动平滑自然,验证了算法的实用性和可靠性。

通过对比分析可以发现,本文提出的改进BiRRT*算法在保持较高搜索效率的同时,显著提升了路径质量,为移动机器人在复杂环境中的自主导航提供了有效的技术支撑。
