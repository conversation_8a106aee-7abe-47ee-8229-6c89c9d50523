clc;
clear;
close all;

% 读取图像
img = imread('3.png');
figure;
imshow(img);
title('原始地图');

% 转换为灰度图像
if size(img, 3) == 3
    gray_img = rgb2gray(img);
else
    gray_img = img;
end

% 二值化处理，提取障碍物
threshold = 128; % 可根据图像调整
binary_img = gray_img < threshold; % 假设黑色区域为障碍物

% 获取图像尺寸
[height, width] = size(binary_img);

% 设置起点和终点（可根据需要调整）
start_point = [50, 50];    % 起点坐标
goal_point = [width-50, height-50]; % 终点坐标

% 从图像提取障碍物信息
obstacles = extract_obstacles_from_image(binary_img);

% 运行路径规划算法
[path_length, total_nodes, time, optimized_path] = birrtstar_image(start_point, goal_point, obstacles, width, height, binary_img);

fprintf('路径长度: %.2f\n', path_length);
fprintf('总节点数: %d\n', total_nodes);
fprintf('计算时间: %.2f秒\n', time);