clc; clear; close all;

% 读取并处理图像
img = imread('3.png');
if size(img, 3) == 3
    gray_img = rgb2gray(img);
else
    gray_img = img;
end

% 二值化处理 - 创建清晰的黑白地图
binary_img = gray_img < 150; % 调整阈值
[height, width] = size(binary_img);

% 设置起点和终点
start_point = [100, height-100];  % 起点
goal_point = [width-100, 100];    % 终点

% 优化的算法参数
max_iter = 3000;  % 增加迭代次数
step_size_min = 5;
step_size_max = 25;
goal_tolerance = 30;
goal_bias = 0.1;  % 降低目标偏置，增加探索
rewire_radius = 40;  % 增大重连半径

% 初始化树
start_nodes = struct('node', start_point, 'parent', -1, 'cost', 0);
goal_nodes = struct('node', goal_point, 'parent', -1, 'cost', 0);

fprintf('开始ADAPT-BiRRT*路径规划...\n');
tic;

% 主算法循环（改进采样策略）
for iter = 1:max_iter
    % 自适应采样策略
    if iter < 500
        % 前期：大范围随机采样
        if rand() < goal_bias
            if mod(iter, 2) == 1
                random_point = goal_point;
            else
                random_point = start_point;
            end
        else
            random_point = [rand() * width, rand() * height];
        end
    elseif iter < 1500
        % 中期：椭圆采样 + 随机采样
        if rand() < 0.3
            % 椭圆采样
            center = (start_point + goal_point) / 2;
            major_axis = norm(goal_point - start_point) / 2 + 100;  % 增大椭圆
            minor_axis = major_axis * 0.8;  % 增大短轴比例
            angle = atan2(goal_point(2) - start_point(2), goal_point(1) - start_point(1));
            
            theta = rand() * 2 * pi;
            r = sqrt(rand());
            x_local = major_axis * r * cos(theta);
            y_local = minor_axis * r * sin(theta);
            
            x_rotated = x_local * cos(angle) - y_local * sin(angle);
            y_rotated = x_local * sin(angle) + y_local * cos(angle);
            
            random_point = center + [x_rotated, y_rotated];
            random_point = max([1, 1], min([width, height], random_point));
        else
            % 随机采样
            random_point = [rand() * width, rand() * height];
        end
    else
        % 后期：增强目标导向
        if rand() < 0.4
            if mod(iter, 2) == 1
                random_point = goal_point;
            else
                random_point = start_point;
            end
        else
            % 在已有节点附近采样
            if mod(iter, 2) == 1 && length(start_nodes) > 1
                base_node = start_nodes(randi(length(start_nodes))).node;
                random_point = base_node + (rand(1,2) - 0.5) * 60;
                random_point = max([1, 1], min([width, height], random_point));
            elseif length(goal_nodes) > 1
                base_node = goal_nodes(randi(length(goal_nodes))).node;
                random_point = base_node + (rand(1,2) - 0.5) * 60;
                random_point = max([1, 1], min([width, height], random_point));
            else
                random_point = [rand() * width, rand() * height];
            end
        end
    end
    
    % 交替扩展两棵树
    if mod(iter, 2) == 1
        [start_nodes, new_idx] = extend_tree_rrt_star(start_nodes, random_point, step_size_min, step_size_max, binary_img, rewire_radius);
    else
        [goal_nodes, new_idx] = extend_tree_rrt_star(goal_nodes, random_point, step_size_min, step_size_max, binary_img, rewire_radius);
    end
    
    % 每50次迭代检查一次连接
    if mod(iter, 50) == 0
        [connected, start_idx, goal_idx] = check_connection(start_nodes, goal_nodes, goal_tolerance, binary_img);
        
        if connected
            path = build_path(start_nodes, goal_nodes, start_idx, goal_idx);
            path = optimize_path_quality(path, binary_img);
            fprintf('在第 %d 次迭代找到路径\n', iter);
            break;
        end
    end
    
    % 显示进度
    if mod(iter, 500) == 0
        fprintf('迭代进度: %d/%d, 起点树节点: %d, 终点树节点: %d\n', ...
                iter, max_iter, length(start_nodes), length(goal_nodes));
    end
end

time_elapsed = toc;

% 创建论文展示图
figure('Position', [100, 100, 800, 600]);

% 显示清晰的黑白地图
imshow(~binary_img, 'InitialMagnification', 'fit'); % 白色背景，黑色障碍物
hold on;

% 绘制起点 - 蓝色圆圈
plot(start_point(1), start_point(2), 'o', 'MarkerSize', 12, 'MarkerFaceColor', 'blue', 'MarkerEdgeColor', 'black', 'LineWidth', 2);

% 绘制终点 - 红色圆圈  
plot(goal_point(1), goal_point(2), 'o', 'MarkerSize', 12, 'MarkerFaceColor', 'red', 'MarkerEdgeColor', 'black', 'LineWidth', 2);

% 绘制路径 - 绿色粗线
if exist('path', 'var')
    for i = 1:size(path, 1)-1
        line([path(i,1), path(i+1,1)], [path(i,2), path(i+1,2)], ...
             'Color', 'green', 'LineWidth', 4);
    end
    
    % 计算路径长度
    path_length = 0;
    for i = 1:size(path, 1)-1
        path_length = path_length + norm(path(i+1,:) - path(i,:));
    end
    
    % 添加标注
    text(start_point(1)-40, start_point(2)-30, 'Start', 'Color', 'blue', 'FontSize', 12, 'FontWeight', 'bold');
    text(goal_point(1)-30, goal_point(2)+30, 'Goal', 'Color', 'red', 'FontSize', 12, 'FontWeight', 'bold');
    
    % 设置标题
    title('ADAPT-BiRRT* Path Planning Result', 'FontSize', 14, 'FontWeight', 'bold');
    
    % 添加图例
    legend_elements = [
        plot(NaN, NaN, 'o', 'MarkerSize', 8, 'MarkerFaceColor', 'blue', 'MarkerEdgeColor', 'black'),
        plot(NaN, NaN, 'o', 'MarkerSize', 8, 'MarkerFaceColor', 'red', 'MarkerEdgeColor', 'black'),
        plot(NaN, NaN, '-', 'Color', 'green', 'LineWidth', 3)
    ];
    legend(legend_elements, {'Start Point', 'Goal Point', 'Planned Path'}, 'Location', 'best', 'FontSize', 10);
    
    % 输出结果
    fprintf('路径规划成功完成!\n');
    fprintf('路径长度: %.2f 像素\n', path_length);
    fprintf('计算时间: %.3f 秒\n', time_elapsed);
    fprintf('总节点数: %d\n', length(start_nodes) + length(goal_nodes));
    
    % 在图上添加统计信息
    text(10, height-30, sprintf('Path Length: %.1f pixels', path_length), ...
         'Color', 'black', 'FontSize', 10, 'BackgroundColor', 'white');
    text(10, height-50, sprintf('Time: %.2f s', time_elapsed), ...
         'Color', 'black', 'FontSize', 10, 'BackgroundColor', 'white');
else
    fprintf('在 %d 次迭代内未找到路径\n', max_iter);
    title('Path Planning Failed', 'FontSize', 14);
end

% 设置坐标轴
axis on;
xlabel('X (pixels)', 'FontSize', 12);
ylabel('Y (pixels)', 'FontSize', 12);
grid on;

%% 辅助函数
function [nearest_idx, nearest_node] = find_nearest(nodes, point)
    min_dist = inf;
    nearest_idx = 1;
    for i = 1:length(nodes)
        dist = norm(nodes(i).node - point);
        if dist < min_dist
            min_dist = dist;
            nearest_idx = i;
        end
    end
    nearest_node = nodes(nearest_idx).node;
end

function collision = check_collision(node1, node2, binary_img)
    [height, width] = size(binary_img);
    
    % 增加采样密度，确保不遗漏障碍物
    distance = norm(node2 - node1);
    num_samples = max(ceil(distance * 2), 50); % 更密集采样
    
    for i = 0:num_samples
        t = i / num_samples;
        point = node1 + t * (node2 - node1);
        x = round(point(1));
        y = round(point(2));
        
        % 更严格的边界检查
        if x < 10 || x > width-9 || y < 10 || y > height-9
            collision = true;
            return;
        end
        
        % 障碍物检查 - 检查周围5x5区域，增大安全边距
        for dx = -2:2
            for dy = -2:2
                check_x = x + dx;
                check_y = y + dy;
                if check_x >= 1 && check_x <= width && check_y >= 1 && check_y <= height
                    if binary_img(check_y, check_x) == 1
                        collision = true;
                        return;
                    end
                end
            end
        end
    end
    collision = false;
end

function [connected, start_idx, goal_idx] = check_connection(start_nodes, goal_nodes, tolerance, binary_img)
    connected = false;
    start_idx = -1;
    goal_idx = -1;
    min_dist = inf;
    
    for i = 1:length(start_nodes)
        for j = 1:length(goal_nodes)
            dist = norm(start_nodes(i).node - goal_nodes(j).node);
            if dist < tolerance && dist < min_dist
                if ~check_collision(start_nodes(i).node, goal_nodes(j).node, binary_img)
                    connected = true;
                    start_idx = i;
                    goal_idx = j;
                    min_dist = dist;
                end
            end
        end
    end
end

function path = build_path(start_nodes, goal_nodes, start_idx, goal_idx)
    % 从起点树回溯
    start_path = [];
    current = start_idx;
    while current ~= -1
        start_path = [start_nodes(current).node; start_path];
        current = start_nodes(current).parent;
    end
    
    % 从终点树回溯
    goal_path = [];
    current = goal_idx;
    while current ~= -1
        goal_path = [goal_path; goal_nodes(current).node];
        current = goal_nodes(current).parent;
    end
    
    % 合并路径
    path = [start_path; goal_path];
end

%% 核心算法函数

% RRT*扩展函数
function [nodes, new_node_idx] = extend_tree_rrt_star(nodes, random_point, step_size_min, step_size_max, binary_img, radius)
    new_node_idx = [];
    
    % 找到最近节点
    [nearest_idx, nearest_node] = find_nearest(nodes, random_point);
    
    % 计算新节点
    direction = (random_point - nearest_node);
    if norm(direction) > 0
        direction = direction / norm(direction);
        step_size = step_size_min + (step_size_max - step_size_min) * rand();
        new_node = nearest_node + direction * step_size;
        
        [height, width] = size(binary_img);
        if new_node(1) > 10 && new_node(1) < width-9 && new_node(2) > 10 && new_node(2) < height-9
            if ~check_collision(nearest_node, new_node, binary_img)
                % RRT*核心：寻找最优父节点
                min_cost = nodes(nearest_idx).cost + norm(new_node - nearest_node);
                best_parent = nearest_idx;
                
                % 在半径内寻找更好的父节点
                for i = 1:length(nodes)
                    if norm(nodes(i).node - new_node) < radius
                        potential_cost = nodes(i).cost + norm(new_node - nodes(i).node);
                        if potential_cost < min_cost && ~check_collision(nodes(i).node, new_node, binary_img)
                            min_cost = potential_cost;
                            best_parent = i;
                        end
                    end
                end
                
                % 添加新节点
                nodes(end+1) = struct('node', new_node, 'parent', best_parent, 'cost', min_cost);
                new_node_idx = length(nodes);
                
                % 重连优化：检查是否可以改善附近节点的路径
                for i = 1:length(nodes)-1
                    if norm(nodes(i).node - new_node) < radius
                        new_cost = min_cost + norm(nodes(i).node - new_node);
                        if new_cost < nodes(i).cost && ~check_collision(new_node, nodes(i).node, binary_img)
                            nodes(i).parent = new_node_idx;
                            nodes(i).cost = new_cost;
                        end
                    end
                end
            end
        end
    end
end

% 路径优化函数 - 高级三次平滑策略
function optimized_path = optimize_path_quality(path, binary_img)
    if size(path, 1) <= 2
        optimized_path = path;
        return;
    end

    fprintf('开始高级三次平滑路径优化...\n');

    % 第一步：分段贪婪算法优化 - 减少关键点
    greedy_path = greedy_path_optimization(path, binary_img);
    fprintf('贪婪算法优化: %d -> %d 节点\n', size(path, 1), size(greedy_path, 1));

    % 第二步：三次Hermite插值平滑
    hermite_path = cubic_hermite_smoothing(greedy_path, binary_img);
    fprintf('三次Hermite平滑: %d -> %d 节点\n', size(greedy_path, 1), size(hermite_path, 1));

    % 第三步：三次贝塞尔曲线平滑
    bezier_path = cubic_bezier_smoothing(hermite_path, binary_img);
    fprintf('三次贝塞尔平滑: %d -> %d 节点\n', size(hermite_path, 1), size(bezier_path, 1));

    % 第四步：PCHIP保形插值最终平滑
    final_path = pchip_final_smoothing(bezier_path, binary_img);
    fprintf('PCHIP最终平滑: %d -> %d 节点\n', size(bezier_path, 1), size(final_path, 1));

    optimized_path = final_path;
end

% 分段贪婪算法
function optimized_path = greedy_path_optimization(path_nodes, binary_img)
    if size(path_nodes, 1) <= 2
        optimized_path = path_nodes;
        return;
    end
    
    optimized_path = path_nodes(1, :);
    current_index = 1;
    
    while current_index < size(path_nodes, 1)
        best_next = current_index + 1;
        
        % 向前搜索最远可达点
        for next_index = current_index + 2:size(path_nodes, 1)
            if ~check_collision_safe(path_nodes(current_index, :), path_nodes(next_index, :), binary_img)
                best_next = next_index;
            else
                break;
            end
        end
        
        optimized_path = [optimized_path; path_nodes(best_next, :)];
        current_index = best_next;
    end
end

% 三次Hermite插值平滑 - 保持切线连续性
function smoothed_path = cubic_hermite_smoothing(path_nodes, binary_img)
    if size(path_nodes, 1) <= 3
        smoothed_path = path_nodes;
        return;
    end

    % 计算每个节点的切线向量
    tangents = zeros(size(path_nodes));
    n = size(path_nodes, 1);

    % 端点切线
    tangents(1, :) = path_nodes(2, :) - path_nodes(1, :);
    tangents(n, :) = path_nodes(n, :) - path_nodes(n-1, :);

    % 中间点切线 - 使用Catmull-Rom方法
    for i = 2:n-1
        tangents(i, :) = 0.5 * (path_nodes(i+1, :) - path_nodes(i-1, :));
    end

    % 归一化切线
    for i = 1:n
        if norm(tangents(i, :)) > 0
            tangents(i, :) = tangents(i, :) / norm(tangents(i, :)) * 20; % 控制切线长度
        end
    end

    % 生成平滑路径
    smoothed_path = [];

    for i = 1:n-1
        p0 = path_nodes(i, :);
        p1 = path_nodes(i+1, :);
        m0 = tangents(i, :);
        m1 = tangents(i+1, :);

        % 计算段长度决定插值密度
        segment_length = norm(p1 - p0);
        num_points = max(10, ceil(segment_length / 2));

        % Hermite插值
        for j = 0:num_points-1
            t = j / (num_points - 1);

            % Hermite基函数
            h00 = 2*t^3 - 3*t^2 + 1;
            h10 = t^3 - 2*t^2 + t;
            h01 = -2*t^3 + 3*t^2;
            h11 = t^3 - t^2;

            % 计算插值点
            point = h00*p0 + h10*m0 + h01*p1 + h11*m1;

            % 碰撞检测
            if isempty(smoothed_path) || ~check_collision_safe(smoothed_path(end, :), point, binary_img)
                smoothed_path = [smoothed_path; point];
            end
        end
    end

    % 确保终点
    if ~isempty(smoothed_path) && norm(smoothed_path(end, :) - path_nodes(end, :)) > 2
        if ~check_collision_safe(smoothed_path(end, :), path_nodes(end, :), binary_img)
            smoothed_path = [smoothed_path; path_nodes(end, :)];
        end
    end
end

% 三次贝塞尔曲线平滑
function smoothed_path = cubic_bezier_smoothing(path_nodes, binary_img)
    if size(path_nodes, 1) <= 3
        smoothed_path = path_nodes;
        return;
    end

    smoothed_path = [];
    n = size(path_nodes, 1);

    for i = 1:n-1
        p0 = path_nodes(i, :);
        p3 = path_nodes(i+1, :);

        % 计算控制点
        direction = p3 - p0;
        control_distance = norm(direction) / 3;

        if i == 1
            % 第一段：使用下一个点的方向
            if n > 2
                next_dir = path_nodes(i+2, :) - p0;
                next_dir = next_dir / norm(next_dir);
            else
                next_dir = direction / norm(direction);
            end
            p1 = p0 + next_dir * control_distance;
        else
            % 使用前一个点的方向
            prev_dir = p0 - path_nodes(i-1, :);
            prev_dir = prev_dir / norm(prev_dir);
            p1 = p0 + prev_dir * control_distance;
        end

        if i == n-1
            % 最后一段：使用前一个点的方向
            prev_dir = p3 - path_nodes(i-1, :);
            prev_dir = prev_dir / norm(prev_dir);
            p2 = p3 - prev_dir * control_distance;
        else
            % 使用下一个点的方向
            next_dir = path_nodes(i+2, :) - p0;
            next_dir = next_dir / norm(next_dir);
            p2 = p3 - next_dir * control_distance;
        end

        % 生成贝塞尔曲线点
        segment_length = norm(p3 - p0);
        num_points = max(15, ceil(segment_length / 1.5));

        for j = 0:num_points-1
            t = j / (num_points - 1);

            % 三次贝塞尔公式
            point = (1-t)^3 * p0 + 3*(1-t)^2*t * p1 + 3*(1-t)*t^2 * p2 + t^3 * p3;

            % 碰撞检测
            if isempty(smoothed_path) || ~check_collision_safe(smoothed_path(end, :), point, binary_img)
                smoothed_path = [smoothed_path; point];
            end
        end
    end

    % 确保终点
    if ~isempty(smoothed_path) && norm(smoothed_path(end, :) - path_nodes(end, :)) > 2
        if ~check_collision_safe(smoothed_path(end, :), path_nodes(end, :), binary_img)
            smoothed_path = [smoothed_path; path_nodes(end, :)];
        end
    end
end

% PCHIP保形插值最终平滑
function final_path = pchip_final_smoothing(path_nodes, binary_img)
    if size(path_nodes, 1) <= 3
        final_path = path_nodes;
        return;
    end

    % 计算弧长参数
    x = path_nodes(:, 1);
    y = path_nodes(:, 2);

    t = zeros(length(x), 1);
    for i = 2:length(t)
        t(i) = t(i-1) + norm(path_nodes(i, :) - path_nodes(i-1, :));
    end

    if t(end) == 0
        final_path = path_nodes;
        return;
    end

    t = t / t(end); % 归一化

    % 计算总路径长度决定插值密度
    total_length = sum(sqrt(diff(x).^2 + diff(y).^2));
    num_points = max(50, ceil(total_length / 1.0)); % 每1像素一个点

    t_new = linspace(0, 1, num_points);

    % PCHIP插值 - 保形性更好
    try
        x_new = pchip(t, x, t_new);
        y_new = pchip(t, y, t_new);
    catch
        % 备用：三次样条
        x_new = spline(t, x, t_new);
        y_new = spline(t, y, t_new);
    end

    % 安全验证和密度控制
    final_path = [x_new(1), y_new(1)];
    min_spacing = 0.8; % 最小间距

    for i = 2:length(x_new)
        candidate = [x_new(i), y_new(i)];

        % 检查间距和碰撞
        if norm(candidate - final_path(end, :)) >= min_spacing
            if ~check_collision_safe(final_path(end, :), candidate, binary_img)
                final_path = [final_path; candidate];
            end
        end
    end

    % 确保终点连接
    end_point = path_nodes(end, :);
    if norm(final_path(end, :) - end_point) > 1.5
        if ~check_collision_safe(final_path(end, :), end_point, binary_img)
            final_path = [final_path; end_point];
        end
    end
end

% 安全碰撞检测函数
function collision = check_collision_safe(node1, node2, binary_img)
    [height, width] = size(binary_img);
    
    distance = norm(node2 - node1);
    num_samples = max(ceil(distance * 3), 100); % 高密度采样
    
    for i = 0:num_samples
        t = i / num_samples;
        point = node1 + t * (node2 - node1);
        x = round(point(1));
        y = round(point(2));
        
        % 严格边界检查
        if x < 15 || x > width-14 || y < 15 || y > height-14
            collision = true;
            return;
        end
        
        % 检查7x7安全区域
        for dx = -3:3
            for dy = -3:3
                check_x = x + dx;
                check_y = y + dy;
                if check_x >= 1 && check_x <= width && check_y >= 1 && check_y <= height
                    if binary_img(check_y, check_x) == 1
                        collision = true;
                        return;
                    end
                end
            end
        end
    end
    collision = false;
end