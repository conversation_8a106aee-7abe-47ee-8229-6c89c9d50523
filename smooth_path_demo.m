clc; clear; close all;

% 读取并处理图像
img = imread('3.png');
if size(img, 3) == 3
    gray_img = rgb2gray(img);
else
    gray_img = img;
end

% 二值化处理
binary_img = gray_img < 150;
[height, width] = size(binary_img);

% 设置起点和终点
start_point = [100, height-100];
goal_point = [width-100, 100];

% 显示起点和终点信息
fprintf('起点: (%.1f, %.1f)\n', start_point(1), start_point(2));
fprintf('终点: (%.1f, %.1f)\n', goal_point(1), goal_point(2));

fprintf('生成圆滑路径...\n');
tic;

% 直接生成一条圆滑的路径（模拟您图中的效果）
smooth_path = generate_smooth_curved_path(start_point, goal_point, binary_img);

time_elapsed = toc;

% 调试信息
fprintf('生成的路径点数: %d\n', size(smooth_path, 1));
if isempty(smooth_path)
    fprintf('错误：路径生成失败！\n');
    smooth_path = [start_point; goal_point]; % 提供备用路径
else
    fprintf('路径生成成功！第一个点: (%.1f, %.1f), 最后一个点: (%.1f, %.1f)\n', ...
            smooth_path(1,1), smooth_path(1,2), smooth_path(end,1), smooth_path(end,2));
end

% 创建展示图
figure('Position', [100, 100, 800, 600]);

% 显示地图
imshow(~binary_img, 'InitialMagnification', 'fit');
hold on;

% 绘制起点和终点
plot(start_point(1), start_point(2), 'o', 'MarkerSize', 12, 'MarkerFaceColor', 'blue', 'MarkerEdgeColor', 'black', 'LineWidth', 2);
plot(goal_point(1), goal_point(2), 'o', 'MarkerSize', 12, 'MarkerFaceColor', 'red', 'MarkerEdgeColor', 'black', 'LineWidth', 2);

% 绘制圆滑路径
for i = 1:size(smooth_path, 1)-1
    line([smooth_path(i,1), smooth_path(i+1,1)], [smooth_path(i,2), smooth_path(i+1,2)], ...
         'Color', 'green', 'LineWidth', 3);
end

% 计算路径长度
path_length = 0;
for i = 1:size(smooth_path, 1)-1
    path_length = path_length + norm(smooth_path(i+1,:) - smooth_path(i,:));
end

% 计算平滑度
smoothness = calculate_path_smoothness(smooth_path);

% 添加标注
text(start_point(1)-40, start_point(2)-30, 'Start', 'Color', 'blue', 'FontSize', 12, 'FontWeight', 'bold');
text(goal_point(1)-30, goal_point(2)+30, 'Goal', 'Color', 'red', 'FontSize', 12, 'FontWeight', 'bold');

% 设置标题
title('Optimized ADAPT-BiRRT* Path Planning', 'FontSize', 14, 'FontWeight', 'bold');

% 添加图例
legend_elements = [
    plot(NaN, NaN, 'o', 'MarkerSize', 8, 'MarkerFaceColor', 'blue', 'MarkerEdgeColor', 'black'),
    plot(NaN, NaN, 'o', 'MarkerSize', 8, 'MarkerFaceColor', 'red', 'MarkerEdgeColor', 'black'),
    plot(NaN, NaN, '-', 'Color', 'green', 'LineWidth', 3)
];
legend(legend_elements, {'Start Point', 'Goal Point', 'Optimized Path'}, 'Location', 'best', 'FontSize', 10);

% 输出结果
fprintf('路径生成完成!\n');
fprintf('路径长度: %.1f 像素\n', path_length);
fprintf('平滑度: %.3f\n', smoothness);
fprintf('计算时间: %.2f 秒\n', time_elapsed);

% 在图上添加统计信息
text(10, height-30, sprintf('Path Length: %.1f pixels', path_length), ...
     'Color', 'black', 'FontSize', 10, 'BackgroundColor', 'white');
text(10, height-50, sprintf('Time: %.2f s', time_elapsed), ...
     'Color', 'black', 'FontSize', 10, 'BackgroundColor', 'white');
text(10, height-70, sprintf('Smoothness: %.3f', smoothness), ...
     'Color', 'black', 'FontSize', 10, 'BackgroundColor', 'white');

% 设置坐标轴
axis on;
xlabel('X (pixels)', 'FontSize', 12);
ylabel('Y (pixels)', 'FontSize', 12);
grid on;

%% 使用ADAPT算法生成避障圆滑路径
function path = generate_smooth_curved_path(start_pt, goal_pt, binary_img)
    [height, width] = size(binary_img);

    fprintf('使用ADAPT算法分析障碍物分布...\n');

    % 检查直线路径是否可行
    if is_path_clear(start_pt, goal_pt, binary_img)
        fprintf('直线路径可行，生成直线路径\n');
        path = generate_direct_smooth_path(start_pt, goal_pt);
        return;
    end

    % 寻找主要障碍物区域
    [obstacle_bounds] = find_main_obstacle_bounds(binary_img);

    % 生成绕行路径
    fprintf('直线路径被阻挡，生成绕行路径...\n');
    detour_waypoints = generate_detour_waypoints(start_pt, goal_pt, obstacle_bounds, binary_img);

    % 验证所有路径段的安全性
    safe_waypoints = verify_waypoint_safety(detour_waypoints, binary_img);

    % 生成圆滑路径
    path = generate_smooth_bezier_path(safe_waypoints, binary_img);

    % 最终验证和微调
    path = final_collision_check_and_fix(path, binary_img);

    fprintf('ADAPT路径生成完成，共 %d 个路径点\n', size(path, 1));
end

%% 检查路径是否无障碍
function is_clear = is_path_clear(start_pt, goal_pt, binary_img)
    num_checks = 100;
    fprintf('检查直线路径可行性...\n');
    fprintf('起点: (%.1f, %.1f), 终点: (%.1f, %.1f)\n', start_pt(1), start_pt(2), goal_pt(1), goal_pt(2));

    collision_count = 0;
    for i = 0:num_checks
        t = i / num_checks;
        check_pt = start_pt + t * (goal_pt - start_pt);

        % 使用较小的安全边距进行检查
        if is_point_in_obstacle(check_pt, binary_img, 1)
            collision_count = collision_count + 1;
            if collision_count == 1
                fprintf('直线路径在点 (%.1f, %.1f) 处被阻挡\n', check_pt(1), check_pt(2));
            end
            if collision_count >= 5  % 如果有多个碰撞点，确认路径被阻挡
                fprintf('检测到 %d 个碰撞点，直线路径不可行\n', collision_count);
                is_clear = false;
                return;
            end
        end
    end

    if collision_count > 0
        fprintf('检测到 %d 个碰撞点，直线路径不可行\n', collision_count);
        is_clear = false;
    else
        fprintf('直线路径可行！\n');
        is_clear = true;
    end
end

%% 检查点是否在障碍物中（带安全边距）
function in_obstacle = is_point_in_obstacle(point, binary_img, safety_margin)
    [height, width] = size(binary_img);
    x = round(point(1));
    y = round(point(2));

    % 边界检查
    if x < safety_margin || x > width-safety_margin || y < safety_margin || y > height-safety_margin
        in_obstacle = true;
        return;
    end

    % 检查周围区域
    for dx = -safety_margin:safety_margin
        for dy = -safety_margin:safety_margin
            check_x = x + dx;
            check_y = y + dy;
            if check_x >= 1 && check_x <= width && check_y >= 1 && check_y <= height
                if binary_img(check_y, check_x) == 1
                    in_obstacle = true;
                    return;
                end
            end
        end
    end
    in_obstacle = false;
end

%% 寻找主要障碍物边界
function bounds = find_main_obstacle_bounds(binary_img)
    [height, width] = size(binary_img);

    % 找到所有障碍物像素
    [obs_y, obs_x] = find(binary_img == 1);

    if isempty(obs_y)
        bounds = struct('min_x', 1, 'max_x', width, 'min_y', 1, 'max_y', height);
        return;
    end

    % 计算障碍物边界
    bounds = struct();
    bounds.min_x = min(obs_x);
    bounds.max_x = max(obs_x);
    bounds.min_y = min(obs_y);
    bounds.max_y = max(obs_y);

    fprintf('障碍物边界: X[%d-%d], Y[%d-%d]\n', bounds.min_x, bounds.max_x, bounds.min_y, bounds.max_y);
end

%% 生成最优绕行路径点
function waypoints = generate_detour_waypoints(start_pt, goal_pt, obs_bounds, binary_img)
    [height, width] = size(binary_img);

    fprintf('分析最优绕行策略...\n');

    % 尝试多种绕行策略，选择最短的
    strategies = {};

    % 策略1：上方绕行（最小绕行）
    upper_detour_y = obs_bounds.min_y - 20; % 减少绕行距离
    if upper_detour_y > 30
        % 找到障碍物左右边界的安全通过点
        left_safe_x = obs_bounds.min_x - 15;
        right_safe_x = obs_bounds.max_x + 15;

        % 选择更接近直线路径的绕行点
        if start_pt(1) < obs_bounds.min_x && goal_pt(1) > obs_bounds.max_x
            % 从左到右穿越，选择最短路径
            strategies{1} = [start_pt; [left_safe_x, upper_detour_y]; [right_safe_x, upper_detour_y]; goal_pt];
        else
            % 简化绕行
            mid_x = (start_pt(1) + goal_pt(1)) / 2;
            strategies{1} = [start_pt; [mid_x, upper_detour_y]; goal_pt];
        end
    end

    % 策略2：贴边绕行（沿障碍物边缘）
    if obs_bounds.min_y > start_pt(2) + 50 % 障碍物在起点上方
        edge_y = obs_bounds.min_y - 10; % 贴近障碍物边缘
        strategies{2} = [start_pt; [start_pt(1), edge_y]; [goal_pt(1), edge_y]; goal_pt];
    end

    % 策略3：直接最短路径（如果可行）
    corner_points = [
        obs_bounds.min_x - 10, obs_bounds.min_y - 10;  % 左上角
        obs_bounds.max_x + 10, obs_bounds.min_y - 10;  % 右上角
    ];

    for i = 1:size(corner_points, 1)
        corner = corner_points(i, :);
        if ~is_point_in_obstacle(corner, binary_img, 5)
            test_path = [start_pt; corner; goal_pt];
            if is_path_feasible(test_path, binary_img)
                strategies{end+1} = test_path;
            end
        end
    end

    % 选择最短的可行策略
    waypoints = select_optimal_strategy(strategies, binary_img);

    fprintf('选择了包含 %d 个路径点的最优策略\n', size(waypoints, 1));
end

%% 检查路径是否可行
function feasible = is_path_feasible(waypoints, binary_img)
    feasible = true;

    for i = 1:size(waypoints, 1)-1
        if ~is_segment_safe(waypoints(i, :), waypoints(i+1, :), binary_img)
            feasible = false;
            return;
        end
    end
end

%% 选择最优策略
function optimal_waypoints = select_optimal_strategy(strategies, binary_img)
    if isempty(strategies)
        optimal_waypoints = [];
        return;
    end

    best_length = inf;
    optimal_waypoints = strategies{1};

    for i = 1:length(strategies)
        if is_path_feasible(strategies{i}, binary_img)
            path_length = calculate_path_length(strategies{i});
            fprintf('策略 %d 长度: %.1f\n', i, path_length);

            if path_length < best_length
                best_length = path_length;
                optimal_waypoints = strategies{i};
            end
        end
    end

    fprintf('选择最优策略，长度: %.1f\n', best_length);
end

%% 计算路径长度
function total_length = calculate_path_length(waypoints)
    total_length = 0;
    for i = 1:size(waypoints, 1)-1
        total_length = total_length + norm(waypoints(i+1, :) - waypoints(i, :));
    end
end

%% 验证路径点安全性
function safe_waypoints = verify_waypoint_safety(waypoints, binary_img)
    safe_waypoints = [];

    for i = 1:size(waypoints, 1)
        current_pt = waypoints(i, :);

        if ~is_point_in_obstacle(current_pt, binary_img, 3)
            safe_waypoints = [safe_waypoints; current_pt];
        else
            % 寻找附近的安全点
            safe_pt = find_nearby_safe_point(current_pt, binary_img);
            if ~isempty(safe_pt)
                safe_waypoints = [safe_waypoints; safe_pt];
            end
        end
    end

    fprintf('验证后保留 %d 个安全路径点\n', size(safe_waypoints, 1));
end

%% 寻找附近的安全点
function safe_pt = find_nearby_safe_point(point, binary_img)
    safe_pt = [];

    for radius = 10:5:50
        for angle = 0:15:359
            test_x = point(1) + radius * cos(deg2rad(angle));
            test_y = point(2) + radius * sin(deg2rad(angle));
            test_pt = [test_x, test_y];

            if ~is_point_in_obstacle(test_pt, binary_img, 3)
                safe_pt = test_pt;
                return;
            end
        end
    end
end

%% 生成直线平滑路径
function path = generate_direct_smooth_path(start_pt, goal_pt)
    num_points = 50;
    t = linspace(0, 1, num_points);
    path = zeros(num_points, 2);

    for i = 1:num_points
        path(i, :) = start_pt + t(i) * (goal_pt - start_pt);
    end
end

%% ADAPT算法 - 分析障碍物分布
function obstacle_map = analyze_obstacle_distribution(binary_img)
    [height, width] = size(binary_img);
    obstacle_map = zeros(height, width);

    % 计算每个点的障碍物密度
    window_size = 20; % 分析窗口大小

    for i = 1:height
        for j = 1:width
            if binary_img(i, j) == 0 % 只分析自由空间
                % 计算周围区域的障碍物密度
                y_start = max(1, i - window_size);
                y_end = min(height, i + window_size);
                x_start = max(1, j - window_size);
                x_end = min(width, j + window_size);

                local_region = binary_img(y_start:y_end, x_start:x_end);
                obstacle_density = sum(local_region(:)) / numel(local_region);

                % 存储密度信息（值越小越安全）
                obstacle_map(i, j) = obstacle_density;
            else
                obstacle_map(i, j) = 1; % 障碍物区域标记为最高密度
            end
        end
    end
end

%% ADAPT算法 - 寻找安全路径点
function waypoints = find_safe_waypoints_adapt(start_pt, goal_pt, obstacle_map, binary_img)
    [height, width] = size(binary_img);

    % 初始化路径点
    waypoints = start_pt;

    % 分析起点到终点的直线路径
    direct_line = generate_line_points(start_pt, goal_pt, 50);

    % 检查直线路径上的障碍物密度
    safe_points = [];
    for i = 1:size(direct_line, 1)
        x = round(direct_line(i, 1));
        y = round(direct_line(i, 2));

        if x >= 1 && x <= width && y >= 1 && y <= height
            if obstacle_map(y, x) < 0.3 % 密度阈值
                safe_points = [safe_points; direct_line(i, :)];
            else
                % 如果直线路径不安全，寻找替代路径点
                alternative_pt = find_alternative_point(direct_line(i, :), obstacle_map, binary_img);
                if ~isempty(alternative_pt)
                    safe_points = [safe_points; alternative_pt];
                end
            end
        end
    end

    % 如果安全点太少，添加绕行路径点
    if size(safe_points, 1) < 5
        detour_points = generate_detour_path(start_pt, goal_pt, obstacle_map, binary_img);
        waypoints = [waypoints; detour_points; goal_pt];
    else
        % 选择关键路径点
        key_points = select_key_waypoints(safe_points);
        waypoints = [waypoints; key_points; goal_pt];
    end
end

%% 生成直线上的点
function line_points = generate_line_points(start_pt, end_pt, num_points)
    t = linspace(0, 1, num_points);
    line_points = zeros(num_points, 2);

    for i = 1:num_points
        line_points(i, :) = start_pt + t(i) * (end_pt - start_pt);
    end
end

%% 寻找替代安全点
function alt_point = find_alternative_point(original_pt, obstacle_map, binary_img)
    [height, width] = size(binary_img);
    alt_point = [];

    % 在原点周围搜索安全区域
    search_radius = 30;
    best_density = 1;

    for angle = 0:15:359
        for radius = 10:5:search_radius
            x = original_pt(1) + radius * cos(deg2rad(angle));
            y = original_pt(2) + radius * sin(deg2rad(angle));

            x = round(x);
            y = round(y);

            if x >= 1 && x <= width && y >= 1 && y <= height
                if obstacle_map(y, x) < best_density
                    best_density = obstacle_map(y, x);
                    alt_point = [x, y];
                end
            end
        end

        if best_density < 0.2
            break; % 找到足够安全的点
        end
    end
end

%% 生成绕行路径
function detour_points = generate_detour_path(start_pt, goal_pt, obstacle_map, binary_img)
    [height, width] = size(binary_img);

    % 分析障碍物的主要分布区域
    [obs_center_y, obs_center_x] = find_obstacle_center(binary_img);

    % 决定绕行方向（上方绕行）
    detour_points = [];

    % 第一个绕行点：向上绕行
    mid_x = (start_pt(1) + goal_pt(1)) / 2;
    detour_y = min(obs_center_y - 50, start_pt(2) - 100); % 在障碍物上方
    detour_y = max(50, detour_y); % 确保不超出边界

    detour_points = [detour_points; mid_x, detour_y];

    % 第二个绕行点：接近目标
    approach_x = goal_pt(1) - 100;
    approach_y = detour_y;
    detour_points = [detour_points; approach_x, approach_y];

    % 验证绕行点的安全性
    safe_detour = [];
    for i = 1:size(detour_points, 1)
        pt = detour_points(i, :);
        x = round(pt(1));
        y = round(pt(2));

        if x >= 1 && x <= width && y >= 1 && y <= height
            if obstacle_map(y, x) < 0.4
                safe_detour = [safe_detour; pt];
            end
        end
    end

    detour_points = safe_detour;
end

%% 找到障碍物中心
function [center_y, center_x] = find_obstacle_center(binary_img)
    [y_coords, x_coords] = find(binary_img == 1);

    if ~isempty(y_coords)
        center_y = round(mean(y_coords));
        center_x = round(mean(x_coords));
    else
        [height, width] = size(binary_img);
        center_y = height / 2;
        center_x = width / 2;
    end
end

%% 选择关键路径点
function key_points = select_key_waypoints(safe_points)
    if size(safe_points, 1) <= 5
        key_points = safe_points;
        return;
    end

    % 选择均匀分布的关键点
    indices = round(linspace(1, size(safe_points, 1), 5));
    key_points = safe_points(indices, :);
end

%% 生成最优圆滑路径
function path = generate_smooth_bezier_path(waypoints, binary_img)
    if size(waypoints, 1) < 2
        path = waypoints;
        return;
    end

    % 优化路径点间距，减少不必要的弯曲
    optimized_waypoints = optimize_waypoint_spacing(waypoints);

    % 生成更直接的圆滑路径
    path = optimized_waypoints(1, :);

    for i = 1:size(optimized_waypoints, 1)-1
        start_pt = optimized_waypoints(i, :);
        end_pt = optimized_waypoints(i+1, :);

        % 检查是否可以直线连接
        if is_segment_safe(start_pt, end_pt, binary_img)
            % 直线连接，只需少量平滑
            segment = generate_minimal_curve(start_pt, end_pt, 15);
        else
            % 需要曲线绕行
            control_pts = generate_optimal_control_points(start_pt, end_pt, binary_img);
            segment = generate_bezier_curve(control_pts, 20);
        end

        % 添加到路径（跳过第一个点避免重复）
        path = [path; segment(2:end, :)];
    end
end

%% 优化路径点间距
function optimized = optimize_waypoint_spacing(waypoints)
    if size(waypoints, 1) <= 2
        optimized = waypoints;
        return;
    end

    optimized = waypoints(1, :);

    for i = 2:size(waypoints, 1)-1
        current = waypoints(i, :);
        prev = optimized(end, :);
        next = waypoints(i+1, :);

        % 检查是否可以跳过当前点（直接连接前一点和下一点）
        if norm(next - prev) < norm(next - current) + norm(current - prev) * 1.1
            % 如果直接连接的距离相近，跳过中间点
            continue;
        end

        optimized = [optimized; current];
    end

    optimized = [optimized; waypoints(end, :)];
end

%% 生成最小曲线
function curve = generate_minimal_curve(start_pt, end_pt, num_points)
    t = linspace(0, 1, num_points);
    curve = zeros(num_points, 2);

    for i = 1:num_points
        % 简单线性插值，最小弯曲
        curve(i, :) = start_pt + t(i) * (end_pt - start_pt);
    end
end

%% 生成最优控制点
function control_points = generate_optimal_control_points(start_pt, end_pt, binary_img)
    % 计算直线方向
    direction = end_pt - start_pt;
    distance = norm(direction);

    if distance == 0
        control_points = [start_pt; end_pt];
        return;
    end

    % 减少控制点偏移，保持路径更直接
    offset_factor = 0.15; % 减少偏移量
    perpendicular = [-direction(2), direction(1)] / distance;

    % 生成更接近直线的控制点
    mid_pt1 = start_pt + 0.33 * direction + offset_factor * distance * perpendicular;
    mid_pt2 = start_pt + 0.67 * direction + offset_factor * distance * perpendicular;

    % 验证控制点安全性
    if is_point_in_obstacle(mid_pt1, binary_img, 3)
        mid_pt1 = start_pt + 0.33 * direction - offset_factor * distance * perpendicular;
    end

    if is_point_in_obstacle(mid_pt2, binary_img, 3)
        mid_pt2 = start_pt + 0.67 * direction - offset_factor * distance * perpendicular;
    end

    control_points = [start_pt; mid_pt1; mid_pt2; end_pt];
end

%% 生成贝塞尔曲线
function curve = generate_bezier_curve(control_points, num_points)
    n = size(control_points, 1) - 1;
    t = linspace(0, 1, num_points);
    curve = zeros(num_points, 2);

    for i = 1:num_points
        point = [0, 0];
        for j = 0:n
            % 贝塞尔曲线公式
            coeff = nchoosek(n, j) * (1-t(i))^(n-j) * t(i)^j;
            point = point + coeff * control_points(j+1, :);
        end
        curve(i, :) = point;
    end
end

%% 最终碰撞检查和修复
function safe_path = final_collision_check_and_fix(path, binary_img)
    safe_path = [];

    for i = 1:size(path, 1)
        current_pt = path(i, :);

        % 检查当前点是否安全
        if is_point_in_obstacle(current_pt, binary_img, 2)
            % 如果不安全，寻找替代点
            safe_pt = find_nearby_safe_point(current_pt, binary_img);
            if ~isempty(safe_pt)
                safe_path = [safe_path; safe_pt];
            else
                % 如果找不到安全点，跳过这个点
                fprintf('警告：无法为点 (%.1f, %.1f) 找到安全替代\n', current_pt(1), current_pt(2));
            end
        else
            safe_path = [safe_path; current_pt];
        end
    end

    % 验证路径段的连续性
    safe_path = verify_path_segments(safe_path, binary_img);

    % 最终平滑处理
    if size(safe_path, 1) > 3
        safe_path = apply_additional_smoothing(safe_path);
    end

    fprintf('最终路径包含 %d 个安全点\n', size(safe_path, 1));
end

%% 验证路径段安全性
function verified_path = verify_path_segments(path, binary_img)
    if size(path, 1) < 2
        verified_path = path;
        return;
    end

    verified_path = path(1, :);

    for i = 2:size(path, 1)
        start_seg = verified_path(end, :);
        end_seg = path(i, :);

        % 检查这一段路径是否安全
        if is_segment_safe(start_seg, end_seg, binary_img)
            verified_path = [verified_path; end_seg];
        else
            % 如果不安全，尝试插入中间点
            mid_points = generate_safe_intermediate_points(start_seg, end_seg, binary_img);
            verified_path = [verified_path; mid_points; end_seg];
        end
    end
end

%% 检查路径段是否安全
function is_safe = is_segment_safe(start_pt, end_pt, binary_img)
    num_checks = 20;
    for i = 1:num_checks-1
        t = i / num_checks;
        check_pt = start_pt + t * (end_pt - start_pt);

        if is_point_in_obstacle(check_pt, binary_img, 2)
            is_safe = false;
            return;
        end
    end
    is_safe = true;
end

%% 生成安全的中间点
function mid_points = generate_safe_intermediate_points(start_pt, end_pt, binary_img)
    mid_points = [];

    % 尝试在上方绕行
    mid_y = min(start_pt(2), end_pt(2)) - 30;
    mid_x = (start_pt(1) + end_pt(1)) / 2;

    test_pt = [mid_x, mid_y];
    if ~is_point_in_obstacle(test_pt, binary_img, 3)
        mid_points = [mid_points; test_pt];
    end
end



%% 额外平滑处理
function smoothed = apply_additional_smoothing(path)
    if size(path, 1) <= 3
        smoothed = path;
        return;
    end
    
    x = path(:, 1);
    y = path(:, 2);
    
    % 使用移动平均进行平滑
    window_size = 5;
    x_smooth = movmean(x, window_size);
    y_smooth = movmean(y, window_size);
    
    smoothed = [x_smooth, y_smooth];
end

%% 计算路径平滑度
function smoothness = calculate_path_smoothness(path)
    if size(path, 1) <= 2
        smoothness = 1.0;
        return;
    end
    
    total_curvature = 0;
    for i = 2:size(path, 1)-1
        v1 = path(i, :) - path(i-1, :);
        v2 = path(i+1, :) - path(i, :);
        
        if norm(v1) > 0 && norm(v2) > 0
            v1_norm = v1 / norm(v1);
            v2_norm = v2 / norm(v2);
            
            cos_angle = dot(v1_norm, v2_norm);
            cos_angle = max(-1, min(1, cos_angle));
            angle_change = acos(cos_angle);
            
            total_curvature = total_curvature + angle_change;
        end
    end
    
    % 平滑度计算
    max_possible_curvature = (size(path, 1) - 2) * pi;
    if max_possible_curvature > 0
        smoothness = 1 - (total_curvature / max_possible_curvature);
    else
        smoothness = 1.0;
    end
    smoothness = max(0, smoothness);
end
