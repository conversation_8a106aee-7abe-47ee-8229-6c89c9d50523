clc;
clear;
close all;

% 起点和终点设置
start_point = [0, 0];    % 起点坐标
goal_point = [100, 100]; % 终点坐标
[path_length, total_nodes, time] = birrtstar(start_point, goal_point);

function [path_length, total_nodes, time] = birrtstar(start_point, goal_point)
    tic; % 开始计时

    % 障碍物设置
    obstacles = create_obstacles(); % 创建障碍物

    % 设置环境参数
    x_max = 100; % X轴最大值
    y_max = 100; % Y轴最大值

    % 可视化环境
    figure;
    hold on;
    axis([0 x_max 0 y_max]);
    axis equal;
    set(gca, 'XLim', [0 x_max], 'YLim', [0 y_max]);
    xlabel('X'); ylabel('Y');
    grid on;
    title('BI-RRT*');

    % 绘制障碍物
    for i = 1:size(obstacles, 1)
        draw_obstacle(obstacles(i, :));
    end

    % 绘制起点和终点
    plot(start_point(1), start_point(2), 'go', 'MarkerSize', 10, 'MarkerFaceColor', 'g'); % 起点
    plot(goal_point(1), goal_point(2), 'ro', 'MarkerSize', 10, 'MarkerFaceColor', 'r'); % 终点

    % 初始化RRT树
    start_tree = struct('node', start_point, 'parent', -1);
    goal_tree = struct('node', goal_point, 'parent', -1);
    start_nodes = start_tree;
    goal_nodes = goal_tree;

    % 设置参数
    max_iter = 1000;
    step_size_min = 0.5;       % 最小步长
    step_size_max = 3;         % 最大步长
    goal_tolerance = 5;
    radius = 10;
    bate=0.8; %障碍物面积对父节点代价的影响
    k_att0 = 1;              % 初始引力系数，控制机器人向目标点的吸引力
    k_rep0 = 1;              % 初始斥力系数，控制机器人远离障碍物的斥力
    rep_range = 10;             % 斥力的影响范围，只有在此范围内的障碍物才会对机器人产生斥力
    n = 2;                     % 斥力计算中的参数，用于控制斥力的形状和强度
    num_density_samples = 50;      % 密度计算的采样点数
    % 计算椭圆参数
    c_x = (start_point(1) + goal_point(1)) / 2; % 椭圆中心x
    c_y = (start_point(2) + goal_point(2)) / 2; % 椭圆中心y
    start_goal_dist = norm(start_point - goal_point); % 起点到终点的距离
    a = start_goal_dist / 2; % 初始长轴长度
    b = a / 3;               % 初始短轴长度

    % 计算椭圆的旋转角度
    delta = goal_point - start_point;
    theta = atan2(delta(2), delta(1)); % 椭圆旋转角度

    nb=15; % 短轴分割点数量
    nc = 0; % 初始化穿过障碍物的线段数
    ellipse_points = generate_ellipse_points(start_point, goal_point, b, nb);

    for i = 1:length(ellipse_points)
        % 绘制从start_point到ellipse_points(i, :)的连线
        line([start_point(1), ellipse_points(i, 1)], [start_point(2), ellipse_points(i, 2)], 'Color', 'b');

        % 绘制从goal_point到ellipse_points(i, :)的连线
        line([goal_point(1), ellipse_points(i, 1)], [goal_point(2), ellipse_points(i, 2)], 'Color', 'm');

        % 检查是否碰撞
        if collision_check_line(start_point, ellipse_points(i, :), obstacles)
            nc = nc + 1;
        end
        if collision_check_line(goal_point, ellipse_points(i, :), obstacles)
            nc = nc + 1;
        end
    end

    % 如果穿过障碍物的线段数量大于阈值，扩大椭圆的短轴
    if nc > nb * 2 / 3
        b = b * 1.1; % 扩大椭圆短轴
    end

    % 绘制椭圆
    plot_ellipse(c_x, c_y, a, b, theta); % 绘制椭圆

    % 开始双向搜索
    for iter = 1:max_iter
        % 每当迭代次数超过最大迭代次数的一半时，进行短轴扩大
        if iter > max_iter / 3 * 2 && mod(iter, max_iter / 10) == 0
            b = b * 1.1; % 扩大椭圆的短轴
            fprintf('Iteration %d: Short axis b expanded to %.2f\n', iter, b); % 打印调试信息
        end

        % 在椭圆内生成随机点
        random_point = generate_random_point_in_ellipse(c_x, c_y, a, b, theta, x_max, y_max);

        % 扩展起点树
        [start_nodes, new_node_index] = extend_tree_rrt_star(start_nodes, goal_point, step_size_min, step_size_max, obstacles, random_point, k_att0, k_rep0, rep_range, n, iter, num_density_samples);
        if ~isempty(new_node_index)
            new_node = start_nodes(new_node_index).node;
            plot(new_node(1), new_node(2), 'bo', 'MarkerSize', 3, 'MarkerFaceColor', 'b');
            if start_nodes(new_node_index).parent ~= -1
                parent_node = start_nodes(start_nodes(new_node_index).parent).node;
                line([parent_node(1), new_node(1)], [parent_node(2), new_node(2)], 'Color', 'b');
            end
            start_nodes = rewire_tree(start_nodes, new_node_index, radius, obstacles, bate);
        end

        % 扩展终点树
        [goal_nodes, new_node_index] = extend_tree_rrt_star(goal_nodes, start_point, step_size_min, step_size_max, obstacles, random_point, k_att0, k_rep0, rep_range, n, iter, num_density_samples);
        if ~isempty(new_node_index)
            new_node = goal_nodes(new_node_index).node;
            plot(new_node(1), new_node(2), 'mo', 'MarkerSize', 3, 'MarkerFaceColor', 'm');
            if goal_nodes(new_node_index).parent ~= -1
                parent_node = goal_nodes(goal_nodes(new_node_index).parent).node;
                line([parent_node(1), new_node(1)], [parent_node(2), new_node(2)], 'Color', 'm');
            end
            goal_nodes = rewire_tree(goal_nodes, new_node_index, radius, obstacles, bate);
        end

        % 检查是否连接
        [connected, start_idx, goal_idx] = is_connected(start_nodes, goal_nodes, goal_tolerance, obstacles);
        if connected
            path_length = path_cost(start_nodes, start_idx) + path_cost(goal_nodes, goal_idx);
            total_nodes = length(start_nodes) + length(goal_nodes);
            start_node = start_nodes(start_idx).node;
            goal_node = goal_nodes(goal_idx).node;
            line([start_node(1), goal_node(1)], [start_node(2), goal_node(2)], 'Color', 'k', 'LineWidth', 2);
            optimized_path = draw_path(start_nodes, goal_nodes, start_idx, goal_idx, obstacles);
            break;
        end
    end

    time = toc;
    % 可视化环境
    figure;
    hold on;
    axis([0 x_max 0 y_max]);
    axis equal;
    set(gca, 'XLim', [0 x_max], 'YLim', [0 y_max]);
    xlabel('X'); ylabel('Y');
    grid on;
    title('分段贪婪算法+三次样条插值法平滑路径');
    % 绘制起点和终点
    plot(start_point(1), start_point(2), 'go', 'MarkerSize', 10, 'MarkerFaceColor', 'g'); % 起点
    plot(goal_point(1), goal_point(2), 'ro', 'MarkerSize', 10, 'MarkerFaceColor', 'r'); % 终点

    % 绘制障碍物
    for i = 1:size(obstacles, 1)
        draw_obstacle(obstacles(i, :));
    end

% 绘制优化后的路径
for i = 1:size(optimized_path, 1) - 1 % 遍历优化路径节点
    line([optimized_path(i, 1), optimized_path(i + 1, 1)], ... % 绘制优化后的路径段
        [optimized_path(i, 2), optimized_path(i + 1, 2)], 'Color', 'g', 'LineWidth', 2);  % 绘制绿色优化路径
end
end

function plot_ellipse(c_x, c_y, a, b, theta)
% 绘制旋转的椭圆
t = linspace(0, 2*pi, 100);
x = a * cos(t);
y = b * sin(t);

% 旋转矩阵
R = [cos(theta), -sin(theta); sin(theta), cos(theta)];
ellipse_points = R * [x; y];

% 平移到椭圆的中心
ellipse_points(1, :) = ellipse_points(1, :) + c_x;
ellipse_points(2, :) = ellipse_points(2, :) + c_y;

plot(ellipse_points(1, :), ellipse_points(2, :), 'b-', 'LineWidth', 2);
end

function random_point = generate_random_point_in_ellipse(c_x, c_y, a, b, theta, x_max, y_max)
% 随机生成一个点在旋转椭圆内
theta_rand = rand() * 2 * pi;  % 随机生成角度 [0, 2π]
r = sqrt(rand());              % 随机生成一个比例 [0, 1]，然后取平方根使得点均匀分布

% 计算椭圆内的 x 和 y 坐标
x = a * r * cos(theta_rand);
y = b * r * sin(theta_rand);

% 旋转并平移
R = [cos(theta), -sin(theta); sin(theta), cos(theta)];
random_point_rot = R * [x; y];
random_point = random_point_rot' + [c_x, c_y];
random_point = max([0, 0], min([x_max, y_max], random_point));
end

function ellipse_points = generate_ellipse_points(start_point, goal_point, b, nb)
% 计算中点
c_x = (start_point(1) + goal_point(1)) / 2;
c_y = (start_point(2) + goal_point(2)) / 2;

% 计算起点和目标点之间的向量
delta_x = goal_point(1) - start_point(1);
delta_y = goal_point(2) - start_point(2);

% 计算垂直于连线的方向向量（短轴方向）
% 将向量 (delta_x, delta_y) 旋转 90 度，得到垂直方向
short_axis_x = -delta_y; % 垂直方向的x分量
short_axis_y = delta_x;  % 垂直方向的y分量

% 归一化短轴方向向量
length = sqrt(short_axis_x^2 + short_axis_y^2);
short_axis_x = short_axis_x / length;
short_axis_y = short_axis_y / length;

% 在短轴上均匀分布7个点，包括中点
ellipse_points = [];

j=(nb-1)/2;
for i = -j:j  % 从 -j 到 j，生成nb个点
    x = c_x + i * b/j * short_axis_x;
    y = c_y + i * b/j * short_axis_y;
    ellipse_points = [ellipse_points; x, y];
end
end

function obstacles = create_obstacles()
% 使用普通数组来创建矩形和圆形障碍物
% 矩形障碍物：[x, y, width, height, 0]
% 圆形障碍物：[x, y, radius, 1]
obstacles = [
     10, 30, 3, 0, 1;
    70, 30, 4, 0, 1;
    90, 30, 4, 0, 1;
    30, 50, 4, 0, 1;
    70, 65, 5, 0, 1;
    50, 70, 5, 0, 1;
     10, 85, 4, 0, 1;
    50,  10, 5, 0, 1;
    30,  10, 4, 0, 1;
     10, 12, 6, 5, 0;
    65,  5, 7, 8, 0;
    85,  5, 9, 11, 0;
    28, 25, 6, 7, 0;
    45, 25, 5, 6, 0;
     10, 70, 5, 0, 1;
     10, 50, 4, 0, 1;
    45, 45, 8, 9, 0;
    65, 45, 7, 8, 0;
    84, 46, 4, 0, 1;
    30, 70, 5, 0, 1;
    84, 65, 7, 9, 0;
    66, 85, 6, 8, 0;
    45, 85, 7, 6, 0;
    25, 85, 6, 10, 0;
    84, 85, 5, 8, 0;
];
end

function draw_obstacle(obstacle)
% 绘制单个障碍物
% 参数 `obstacle` 包含障碍物的定义信息
% 根据障碍物类型（矩形或圆形）进行绘制

if obstacle(5) == 1
    % 如果障碍物类型为圆形
    pos = obstacle(1:2);         % 圆心坐标 [x, y]
    radius = obstacle(3);        % 圆的半径
    % 使用rectangle函数绘制圆形，设置圆的外接矩形位置、曲率为1表示圆形
    rectangle('Position', [pos(1) - radius, pos(2) - radius, 2 * radius, 2 * radius], ...
        'Curvature', [1, 1], ...
        'FaceColor', [0.3, 0.6, 0.8, 0.95], ... % 设置填充颜色和透明度
        'EdgeColor', [0, 0, 0]);              % 设置边界颜色为黑色
else
    % 如果障碍物类型为矩形
    rectangle('Position', obstacle(1:4), ...
        'FaceColor', [0.3, 0.6, 0.8, 0.95], ... % 设置填充颜色和透明度
        'EdgeColor', [0.1, 0.1, 0.1]);        % 设置边界颜色为深灰色
end
end

% 扩展树的函数（标准RRT*）
function [nodes, new_node_index, nearest_node] = extend_tree_rrt_star(nodes, target_point, step_size_min, step_size_max, obstacles, random_point, k_att, k_rep, rep_range, n, iter, num_density_samples)
% 生成随机点

% 找到最近的节点
[nearest_node_struct, nearest_index] = find_nearest_node(nodes, random_point);
nearest_node = nearest_node_struct.node;

cn = compute_density(nearest_node, obstacles, 20, num_density_samples);
step_size = step_size_min + (step_size_max - step_size_min) * exp(-cn); % 步长随着密度降低而增加

nearest_node_goal_struct=find_nearest_node(nodes, target_point); % 找到离目标最近的节点
nearest_node_goal = nearest_node_goal_struct.node;
cg = compute_density(nearest_node_goal, obstacles, 20, num_density_samples);
base_goal_bias=0.2; %基础目标偏置概率
goal_bias = max(base_goal_bias * exp(-cg) * exp(-iter / 100), 0.1); % 随着迭代次数的增加，概率降低

if rand() < goal_bias
    % 如果满足目标偏置概率，向目标点方向生成新节点
    direction = (target_point - nearest_node) / norm(target_point - nearest_node); % 计算方向单位向量
    new_node = nearest_node + direction * step_size; % 计算新节点坐标
else
    % 否则，使用人工势场方法计算方向并生成新节点
    apf_direction = compute_apf(nearest_node, target_point, random_point, obstacles, k_att, k_rep, rep_range, n, iter, num_density_samples);
    new_node = nearest_node + apf_direction * step_size; % 计算新节点坐标
end

% 碰撞检测
if ~collision_check_line(nearest_node, new_node, obstacles)
    nodes(end + 1) = struct('node', new_node, 'parent', nearest_index);
    new_node_index = length(nodes);
else
    new_node_index = [];
end
end

% 检查是否有起点树和终点树之间的连接
function [connected, start_idx, goal_idx] = is_connected(start_nodes, goal_nodes, tolerance, obstacles)
connected = false; % 初始化连接状态为假
start_idx = -1; % 初始化起点索引
goal_idx = -1;  % 初始化终点索引
min_distf = inf; % 记录最小距离以确定最近的连接，inf是无穷大

for i = 1:length(start_nodes) % 遍历所有起点树节点
    for j = 1:length(goal_nodes) % 遍历所有终点树节点
        dist = norm(start_nodes(i).node - goal_nodes(j).node); % 计算节点之间的距离
        if dist < tolerance && dist < min_distf % 如果距离在容忍范围内且小于当前最小距离
            % 检查节点之间的线段是否无碰撞
            if ~collision_check_line(start_nodes(i).node, goal_nodes(j).node, obstacles)
                connected = true; % 标记为连接
                start_idx = i; % 记录起点索引
                goal_idx = j;  % 记录终点索引
                min_distf = dist; % 更新最小距离
            end
        end
    end
end
end

% 优化树的重连函数
function nodes = rewire_tree(nodes, new_node_index, radius, obstacles, bate)
new_node = nodes(new_node_index).node;  % 新节点的坐标
min_cost = inf;  % 初始化最小成本为无穷大
best_parent_index = -1;  % 初始化最优父节点索引
for i = 1:length(nodes) % 遍历所有节点
    if i ~= new_node_index % 不处理新节点自身
        if norm(nodes(i).node - new_node) < radius  % 如果节点在重连半径内
            % 检查节点之间的线段是否无碰撞
            if ~collision_check_line(nodes(i).node, new_node, obstacles)
                midpoint = compute_midpoint(nodes(i).node, new_node);
                area = compute_intersection_area(midpoint, radius, obstacles);
                new_cost = path_cost(nodes, i) + norm(nodes(i).node - new_node);
                if new_cost > radius^2
                    new_cost = new_cost + area * bate; % 根据潜在父节点和新节点之间的障碍物面积更新成本（扩展一段时间面积占比开始小于路径长度代价）
                else
                    new_cost = new_cost + area * bate * 0.1;% 根据潜在父节点和新节点之间的障碍物面积更新成本（刚开始扩展路径长度代价远远小于面积占比）
                end
                % 如果新成本更低，则更新最小成本和最优父节点
                if new_cost < min_cost
                    min_cost = new_cost;
                    best_parent_index = i;  % 将最优父节点设置为当前节点
                end
            end
        end
    end
end

% 如果找到了最优父节点，更新新节点的父节点
if best_parent_index ~= -1
    nodes(new_node_index).parent = best_parent_index; % 更新新节点的父节点为最优父节点
end
end


% 计算从根节点到给定节点的路径成本
function cost = path_cost(nodes, index)
cost = 0; % 初始化成本为0
current = nodes(index); % 从目标节点开始
% 从目标节点遍历到根节点，累加成本
while current.parent ~= -1 % 当当前节点不是根节点
    parent = nodes(current.parent); % 获取当前节点的父节点
    cost = cost + norm(current.node - parent.node);  % 累加路径段的距离
    current = parent;  % 继续向上回溯
end
end

function midpoint = compute_midpoint(node1, node2)
    % 计算两点 node1 和 node2 组成的线段的中心点坐标
    % node1 和 node2 是两个二维坐标点 [x, y]
    
    midpoint = [(node1(1) + node2(1)) / 2, (node1(2) + node2(2)) / 2];
end

% 绘制路径函数
function optimized_path = draw_path(start_nodes, goal_nodes, start_idx, goal_idx, obstacles)
% 回溯起点树路径
path_nodes_start = []; % 初始化起点路径节点数组
current_node = start_nodes(start_idx);  % 从起点树中与终点树连接的节点开始回溯

while current_node.parent ~= -1 % 当当前节点不是根节点
    path_nodes_start = [current_node.node; path_nodes_start];  % 将当前节点插入路径
    current_node = start_nodes(current_node.parent);  % 回溯到父节点
end
path_nodes_start = [current_node.node; path_nodes_start];  % 添加起点

% 回溯终点树路径
path_nodes_goal = []; % 初始化终点路径节点数组
current_node = goal_nodes(goal_idx);  % 从终点树中与起点树连接的节点开始回溯

while current_node.parent ~= -1 % 当当前节点不是根节点
    path_nodes_goal = [path_nodes_goal; current_node.node];  % 将当前节点插入路径
    current_node = goal_nodes(current_node.parent);  % 回溯到父节点
end
path_nodes_goal = [path_nodes_goal; current_node.node];  % 添加终点

% 合并路径
path_nodes = [path_nodes_start; path_nodes_goal];  % 合并起点树路径和终点树路径

% 绘制原始路径
for i = 1:size(path_nodes, 1) - 1 % 遍历所有路径节点
    line([path_nodes(i, 1), path_nodes(i + 1, 1)], ... % 绘制每条路径段
        [path_nodes(i, 2), path_nodes(i + 1, 2)], 'Color', 'r', 'LineWidth', 2);  % 绘制黑色线条
end

% 调用优化函数
optimized_path = optimize_path(path_nodes, obstacles); % 优化路径

% 绘制优化后的路径
for i = 1:size(optimized_path, 1) - 1 % 遍历优化路径节点
    line([optimized_path(i, 1), optimized_path(i + 1, 1)], ... % 绘制优化后的路径段
        [optimized_path(i, 2), optimized_path(i + 1, 2)], 'Color', 'g', 'LineWidth', 2);  % 绘制绿色优化路径
end

%计算并输出路径长度
optimized_path_length = 0; % 初始化优化路径长度为0
for i = 1:size(optimized_path, 1) - 1 % 遍历优化路径节点
    optimized_path_length = optimized_path_length + norm(optimized_path(i + 1, :) - optimized_path(i, :));  % 累加路径长度
end
end

% 路径优化函数（保持不变）
function optimized_path = optimize_path(path_nodes, obstacles)
max_dist = 20;
% 初始化优化后的路径，以起点开始
optimized_path = path_nodes(1, :);

% 当前路径索引，从第一个节点开始
current_index = 1;

% 贪婪地移除无用节点，逐步构建简化后的路径
while current_index < size(path_nodes, 1)
    % 从当前节点开始，尝试跳过若干个节点
    next_index = current_index + 1;

    % 向前搜索，找到最远的没有碰撞且距离小于 max_dist 的节点
    while next_index <= size(path_nodes, 1) && ...
            ~collision_check_line(path_nodes(current_index, :), path_nodes(next_index, :), obstacles) && ...
            norm(path_nodes(current_index, :) - path_nodes(next_index, :)) <= max_dist
        % 没有碰撞且距离在限制内，继续尝试更远的节点
        next_index = next_index + 1;
    end

    % 找到第一个不满足条件的节点或路径终点，选择上一个符合条件的节点
    optimized_path = [optimized_path; path_nodes(next_index - 1, :)];

    % 更新当前节点索引，准备在简化路径基础上继续贪婪搜索
    current_index = next_index - 1;
end
optimized_path = optimize_path1(optimized_path, obstacles);
end

function optimized_path = optimize_path1(path_nodes, obstacles)
    % 初始化插值参数
    % 假设路径节点是二维点 (x, y)，如果是三维路径，可以调整此部分
    x = path_nodes(:, 1);
    y = path_nodes(:, 2);

    % 创建一个细化的插值区间（例如插值间隔为0.1）
    interpolation_points = linspace(1, length(path_nodes), 100);  % 100个插值点，根据需要调整

    % 使用三次样条插值
    x_interpolated = spline(1:length(path_nodes), x, interpolation_points);
    y_interpolated = spline(1:length(path_nodes), y, interpolation_points);

    % 将插值结果组合成平滑路径
    optimized_path = [x_interpolated', y_interpolated'];

    % 可选择性地，如果需要进一步检查路径是否存在碰撞，可以加入碰撞检测
    % for i = 1:length(optimized_path)-1
    %     if collision_check_line(optimized_path(i, :), optimized_path(i+1, :), obstacles)
    %         % 处理碰撞
    %     end
    % end
end


% 找到距离给定点最近的节点
function [nearest_node_struct, nearest_index] = find_nearest_node(nodes, point)
min_dist = inf; % 初始化最小距离为无穷大
nearest_node_struct = []; % 初始化最近节点为空
nearest_index = -1; % 初始化最近节点索引为-1（表示未找到）

% 遍历所有节点，计算与给定点的距离
for i = 1:length(nodes)
    dist = norm(nodes(i).node - point); % 计算当前节点到给定点的距离
    if dist < min_dist
        min_dist = dist; % 更新最小距离
        nearest_node_struct = nodes(i); % 更新最近节点
        nearest_index = i; % 更新最近节点索引
    end
end
end

% 碰撞检测函数，检查点是否与障碍物发生碰撞
function collision = collision_check(point, obstacles)
collision = false;
for i = 1:size(obstacles, 1)
    obs = obstacles(i, :);

    if obs(5) == 1  % 圆形障碍物
        center = obs(1:2);
        radius = obs(3);
        if norm(point - center) <= radius
            collision = true;
            return;
        end
    elseif obs(5) == 0  % 矩形障碍物
        if point(1) >= obs(1) && point(1) <= obs(1) + obs(3) && ...
                point(2) >= obs(2) && point(2) <= obs(2) + obs(4)
            collision = true;
            return;
        end
    end
end
end

% 碰撞检测函数，检查两节点之间的线段是否与障碍物发生碰撞
function collision = collision_check_line(node1, node2, obstacles)
collision = false;
steps = 10 * ceil(norm(node2 - node1) / 0.5);  % 使用较小的步长来提高插值精度
for i = 1:steps
    % 在node1和node2之间插值
    point = node1 + (node2 - node1) * (i / steps);
    % 检查插值点是否与任何障碍物发生碰撞
    if collision_check(point, obstacles)  % 调用修改后的 collision_check 函数
        collision = true;
        return; % 一旦检测到碰撞，退出函数
    end
end
end
