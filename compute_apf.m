function apf_direction = compute_apf(pos, goal_pos, rand_point, obstacles, k_att0, k_rep0, rep_range, n, iter, num_density_samples)
% 计算人工势场的合力方向，用于引导节点向目标点移动并避开障碍物
% 输入参数：
%   pos - 当前节点的位置坐标 [x, y]
%   goal_pos - 目标点的位置坐标 [x, y]
%   rand_point - 随机采样点的位置坐标 [x, y]
%   obstacles - 障碍物列表
%   k_att0 - 初始引力系数
%   k_rep0 - 初始斥力系数
%   rep_range - 斥力的影响范围
%   n - 斥力计算的参数
%   iter - 当前的迭代次数
% 输出：
%   apf_direction - 合力方向的单位向量 [dx, dy]

% 参数设置

density_beta = 1.5;            % 密度对斥力的指数影响系数（关键修改点）

if iter > 500
    k_att = k_att0 * exp(-0.005 * (iter - 500)); 
    k_rep_base = k_rep0 * exp(-0.005 * (iter - 500)); 
else
    k_att = k_att0;
    k_rep_base = k_rep0;
end

% 计算障碍物密度（0到1之间）
density = compute_density(pos, obstacles, 20, num_density_samples);

% 指数型斥力系数公式（关键修改点）
% 公式说明：k_rep = 基础斥力系数 × e^(beta×密度)
% - 当密度=0时，k_rep = k_rep_base（保持基准值）
% - 当密度=1时，k_rep = k_rep_base × e^beta（最大增强）
k_rep = k_rep_base * exp(density_beta * density);

% 计算目标点吸引力
F_att_goal = -k_att * (pos - goal_pos);

% 随机点吸引力
k_rand = 6;
F_att_rand = -k_rand * (pos - rand_point);

% 总引力
F_att = F_att_goal + F_att_rand;

% 初始化斥力
F_rep = [0, 0];

% 计算所有障碍物的斥力
for i = 1:size(obstacles, 1)
    obs = obstacles(i, :);
    if obs(5) == 0  % 矩形障碍物
        obs_min = obs(1:2);
        obs_max = obs(1:2) + obs(3:4);
        [dist, proj_point] = point2box(pos, obs_min, obs_max, obs);
    else            % 圆形障碍物
        center = obs(1:2);
        radius = obs(3);
        [dist, proj_point] = point2circle(pos, center, radius);
    end

    if dist < rep_range && dist > 0
        rho_g = norm(pos - goal_pos);
        dist = max(dist, 3);  % 避免过大的斥力

        % 传统斥力分量
        F_rep1 = k_rep * (1/dist - 1/rep_range) * (rho_g^2) / (dist^2) * (pos - proj_point) / dist;
        % 附加斥力分量
        F_rep2 = (n / 2) * k_rep * (1/dist - 1/rep_range)^2 * (rho_g^(n-1)) * (pos - goal_pos) / rho_g;
        F_rep = F_rep + F_rep1 + F_rep2;
    end
end

% 计算总合力并归一化
F_total = F_att + F_rep;
if norm(F_total) > 0
    apf_direction = F_total / norm(F_total);
else
    apf_direction = [0, 0];
end
end


function [dist, proj_point] = point2circle(point, center, radius)
% 计算点到圆形障碍物的最近距离和投影点
vec = point - center;
dist_to_center = norm(vec);
if dist_to_center <= radius
    dist = 0;
    proj_point = point;  % 点在圆内，最近点为自身
else
    dist = dist_to_center - radius;
    proj_point = center + (vec / dist_to_center) * radius;
end
end

function [dist, proj_point] = point2box(point, box_min, box_max, obs)
% 计算点到矩形障碍物的最近距离和投影点（已适配圆形调用）
if obs(5) == 1  % 适配原point2box中的圆形处理
    center = obs(1:2);
    radius = obs(3);
    [dist, proj_point] = point2circle(point, center, radius);
    return;
end

% 处理矩形
proj_x = max(box_min(1), min(point(1), box_max(1)));
proj_y = max(box_min(2), min(point(2), box_max(2)));
proj_point = [proj_x, proj_y];
dist = norm(point - proj_point);
end