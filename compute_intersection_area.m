function area = compute_intersection_area(new_node, radius, obstacles)
    % new_node 是新节点坐标，radius 是半径，obstacles 是障碍物数据
    
    area = 0;  % 初始化交集面积
    
    % 遍历所有障碍物
    for i = 1:size(obstacles, 1)
        % 获取障碍物的坐标和形状信息
        obs_x = obstacles(i, 1);
        obs_y = obstacles(i, 2);
        obs_radius = obstacles(i, 3);
        obs_type = obstacles(i, 5);  % 最后一位，1是圆形，0是矩形
        
        if obs_type == 1
            % 处理圆形障碍物
            dist = sqrt((new_node(1) - obs_x)^2 + (new_node(2) - obs_y)^2);
            if dist < (radius + obs_radius)  % 圆形相交
                intersection = circle_intersection_area(radius, obs_radius, dist);
                area = area + intersection;  % 累加交集面积
            end
        elseif obs_type == 0
            % 处理矩形障碍物
            intersection = rectangle_circle_intersection_area(new_node, radius, obs_x, obs_y, obs_radius);
            area = area + intersection;  % 累加交集面积
        end
    end
end

% 计算两个圆形障碍物的交集面积
function intersection_area = circle_intersection_area(radius1, radius2, distance)
    % 如果两个圆没有交集，返回面积为0
    if distance >= (radius1 + radius2)
        intersection_area = 0;
        return;
    end
    
    % 如果一个圆完全包含在另一个圆内，返回小圆的面积
    if distance <= abs(radius1 - radius2)
        intersection_area = pi * min(radius1, radius2)^2;
        return;
    end
    
    % 计算两个圆的交集面积
    r1_sq = radius1^2;
    r2_sq = radius2^2;
    d_sq = distance^2;
    
    part1 = r1_sq * acos((d_sq + r1_sq - r2_sq) / (2 * distance * radius1));
    part2 = r2_sq * acos((d_sq + r2_sq - r1_sq) / (2 * distance * radius2));
    part3 = 0.5 * sqrt((-distance + radius1 + radius2) * (distance + radius1 - radius2) ...
                       * (distance - radius1 + radius2) * (distance + radius1 + radius2));
    
    intersection_area = part1 + part2 - part3;  % 总交集面积
end

% 计算圆形与矩形的交集面积
function intersection_area = rectangle_circle_intersection_area(center, radius, rect_x, rect_y, rect_radius)
    % 计算圆与矩形之间的交集面积
    % 矩形坐标 (rect_x, rect_y) 和大小 (rect_radius) 是一个近似的假设
    % 假设矩形是正方形或者宽高相等的矩形
    % 对于一个圆与矩形的交集，通常我们需要计算四个边界和圆之间的交集。
    % 这里提供一种估算的方法：
    
    % 计算矩形与圆的交集的面积（这个是一个近似方法，可以进一步精化）
    % 如果圆心在矩形内，面积计算较简单，可以直接算出交集区域
    % 这里的计算较为简化，针对更复杂的矩形形状可以考虑更精细的方法

    % 假设矩形为正方形，边长为 2 * rect_radius
    rect_half = rect_radius;
    x1 = rect_x - rect_half;
    x2 = rect_x + rect_half;
    y1 = rect_y - rect_half;
    y2 = rect_y + rect_half;

    % 计算圆心到矩形的最近距离
    dist_x = max(0, max(x1 - center(1), center(1) - x2));
    dist_y = max(0, max(y1 - center(2), center(2) - y2));

    % 如果圆心离矩形太远，没有交集
    if dist_x^2 + dist_y^2 >= radius^2
        intersection_area = 0;
    else
        % 否则，计算一个简化的交集面积
        % 这里只做简单的估算，精确的计算方法需要更复杂的几何方法
        intersection_area = pi * radius^2 / 4;  % 这里简化为一个四分之一圆的面积
    end
end
