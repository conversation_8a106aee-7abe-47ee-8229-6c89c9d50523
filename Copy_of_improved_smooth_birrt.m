clc; clear; close all;

% 读取并处理图像
img = imread('3.png');
if size(img, 3) == 3
    gray_img = rgb2gray(img);
else
    gray_img = img;
end

% 二值化处理 - 创建清晰的黑白地图
binary_img = gray_img < 150; % 调整阈值
[height, width] = size(binary_img);

% 设置起点和终点
start_point = [100, height-200];  % 起点
goal_point = [width-200, 200];    % 终点

% 优化的算法参数
max_iter = 3000;  % 增加迭代次数
step_size_min = 5;
step_size_max = 25;
goal_tolerance = 23;
goal_bias = 0.3;  % 降低目标偏置，增加探索
rewire_radius = 40;  % 增大重连半径

% 初始化树
start_nodes = struct('node', start_point, 'parent', -1, 'cost', 0);
goal_nodes = struct('node', goal_point, 'parent', -1, 'cost', 0);

fprintf('开始ADAPT-BiRRT*路径规划...\n');
tic;

% 主算法循环（改进采样策略）
for iter = 1:max_iter
    % 自适应采样策略
    if iter < 500
        % 前期：大范围随机采样
        if rand() < goal_bias
            if mod(iter, 2) == 1
                random_point = goal_point;
            else
                random_point = start_point;
            end
        else
            random_point = [rand() * width, rand() * height];
        end
    elseif iter < 1500
        % 中期：椭圆采样 + 随机采样
        if rand() < 0.3
            % 椭圆采样
            center = (start_point + goal_point) / 2;
            major_axis = norm(goal_point - start_point) / 2 + 100;  % 增大椭圆
            minor_axis = major_axis * 0.8;  % 增大短轴比例
            angle = atan2(goal_point(2) - start_point(2), goal_point(1) - start_point(1));
            
            theta = rand() * 2 * pi;
            r = sqrt(rand());
            x_local = major_axis * r * cos(theta);
            y_local = minor_axis * r * sin(theta);
            
            x_rotated = x_local * cos(angle) - y_local * sin(angle);
            y_rotated = x_local * sin(angle) + y_local * cos(angle);
            
            random_point = center + [x_rotated, y_rotated];
            random_point = max([1, 1], min([width, height], random_point));
        else
            % 随机采样
            random_point = [rand() * width, rand() * height];
        end
    else
        % 后期：增强目标导向
        if rand() < 0.4
            if mod(iter, 2) == 1
                random_point = goal_point;
            else
                random_point = start_point;
            end
        else
            % 在已有节点附近采样
            if mod(iter, 2) == 1 && length(start_nodes) > 1
                base_node = start_nodes(randi(length(start_nodes))).node;
                random_point = base_node + (rand(1,2) - 0.5) * 60;
                random_point = max([1, 1], min([width, height], random_point));
            elseif length(goal_nodes) > 1
                base_node = goal_nodes(randi(length(goal_nodes))).node;
                random_point = base_node + (rand(1,2) - 0.5) * 60;
                random_point = max([1, 1], min([width, height], random_point));
            else
                random_point = [rand() * width, rand() * height];
            end
        end
    end
    
    % 交替扩展两棵树
    if mod(iter, 2) == 1
        [start_nodes, new_idx] = extend_tree_rrt_star(start_nodes, random_point, step_size_min, step_size_max, binary_img, rewire_radius);
    else
        [goal_nodes, new_idx] = extend_tree_rrt_star(goal_nodes, random_point, step_size_min, step_size_max, binary_img, rewire_radius);
    end
    
    % 每50次迭代检查一次连接
    if mod(iter, 50) == 0
        [connected, start_idx, goal_idx] = check_connection(start_nodes, goal_nodes, goal_tolerance, binary_img);
        
        if connected
            path = build_path(start_nodes, goal_nodes, start_idx, goal_idx);
            % 关键改进：先进行路径优化，再进行平滑处理
            path = optimize_path_greedy(path, binary_img);  % 贪婪路径优化
            path = generate_ultra_smooth_curve(path, binary_img);  % 生成超平滑曲线
            fprintf('在第 %d 次迭代找到路径\n', iter);
            break;
        end
    end
    
    % 显示进度
    if mod(iter, 500) == 0
        fprintf('迭代进度: %d/%d, 起点树节点: %d, 终点树节点: %d\n', ...
                iter, max_iter, length(start_nodes), length(goal_nodes));
    end
end

time_elapsed = toc;

% 创建论文展示图
figure('Position', [100, 100, 800, 600]);

% 显示清晰的黑白地图
imshow(~binary_img, 'InitialMagnification', 'fit'); % 白色背景，黑色障碍物
hold on;

% 绘制起点 - 蓝色圆圈
plot(start_point(1), start_point(2), 'o', 'MarkerSize', 12, 'MarkerFaceColor', 'blue', 'MarkerEdgeColor', 'black', 'LineWidth', 2);

% 绘制终点 - 红色圆圈  
plot(goal_point(1), goal_point(2), 'o', 'MarkerSize', 12, 'MarkerFaceColor', 'red', 'MarkerEdgeColor', 'black', 'LineWidth', 2);

% 绘制路径 - 平滑绿色曲线
if exist('path', 'var')
    % 使用plot函数绘制平滑曲线，更细的线条
    plot(path(:,1), path(:,2), 'Color', 'green', 'LineWidth', 2, 'LineStyle', '-');

    % 计算路径长度
    path_length = 0;
    for i = 1:size(path, 1)-1
        path_length = path_length + norm(path(i+1,:) - path(i,:));
    end

    % 添加标注
    text(start_point(1)-40, start_point(2)-30, 'Start', 'Color', 'blue', 'FontSize', 12, 'FontWeight', 'bold');
    text(goal_point(1)-30, goal_point(2)+30, 'Goal', 'Color', 'red', 'FontSize', 12, 'FontWeight', 'bold');

    % 设置标题
    title('Optimized ADAPT-BiRRT* Path Planning', 'FontSize', 14, 'FontWeight', 'bold');

    % 添加图例
    legend_elements = [
        plot(NaN, NaN, 'o', 'MarkerSize', 8, 'MarkerFaceColor', 'blue', 'MarkerEdgeColor', 'black'),
        plot(NaN, NaN, 'o', 'MarkerSize', 8, 'MarkerFaceColor', 'red', 'MarkerEdgeColor', 'black'),
        plot(NaN, NaN, '-', 'Color', 'green', 'LineWidth', 2)
    ];
    legend(legend_elements, {'Start Point', 'Goal Point', 'Optimized Path'}, 'Location', 'best', 'FontSize', 10);

    % 输出结果
    fprintf('路径规划成功完成!\n');
    fprintf('路径长度: %.2f 像素\n', path_length);
    fprintf('计算时间: %.3f 秒\n', time_elapsed);
    fprintf('总节点数: %d\n', length(start_nodes) + length(goal_nodes));

    % 在图上添加统计信息
    text(10, height-30, sprintf('Path Length: %.1f pixels', path_length), ...
         'Color', 'black', 'FontSize', 10, 'BackgroundColor', 'white');
    text(10, height-50, sprintf('Time: %.2f s', time_elapsed), ...
         'Color', 'black', 'FontSize', 10, 'BackgroundColor', 'white');
else
    fprintf('在 %d 次迭代内未找到路径\n', max_iter);
    title('Path Planning Failed', 'FontSize', 14);
end

% 设置坐标轴
axis on;
xlabel('X (pixels)', 'FontSize', 12);
ylabel('Y (pixels)', 'FontSize', 12);
grid on;

%% 生成超平滑曲线 - 类似论文图片效果
function ultra_smooth_path = generate_ultra_smooth_curve(path, binary_img)
    if size(path, 1) <= 2
        ultra_smooth_path = path;
        return;
    end

    fprintf('开始生成超平滑曲线...\n');

    % 第一步：贪婪算法优化，获得关键路径点
    key_points = aggressive_greedy_optimization(path, binary_img);
    fprintf('关键点提取: %d -> %d 个点\n', size(path, 1), size(key_points, 1));

    % 第二步：生成高密度平滑曲线
    ultra_smooth_path = generate_high_density_smooth_curve(key_points, binary_img);
    fprintf('超平滑曲线生成: %d -> %d 个点\n', size(key_points, 1), size(ultra_smooth_path, 1));

    fprintf('超平滑曲线生成完成！\n');
end

% 生成高密度平滑曲线
function smooth_curve = generate_high_density_smooth_curve(key_points, binary_img)
    if size(key_points, 1) <= 2
        smooth_curve = key_points;
        return;
    end

    % 使用分段三次Hermite插值生成超平滑曲线
    x = key_points(:, 1);
    y = key_points(:, 2);
    n = length(x);

    % 计算弧长参数
    t = zeros(n, 1);
    for i = 2:n
        t(i) = t(i-1) + norm(key_points(i, :) - key_points(i-1, :));
    end

    if t(end) == 0
        smooth_curve = key_points;
        return;
    end

    t = t / t(end); % 归一化到[0,1]

    % 计算总路径长度，确定插值密度
    total_length = sum(sqrt(diff(x).^2 + diff(y).^2));
    % 每0.2像素一个点，生成更细腻的曲线
    num_points = max(200, ceil(total_length / 0.2));

    t_new = linspace(0, 1, num_points);

    % 使用PCHIP插值生成平滑曲线
    try
        x_smooth = pchip(t, x, t_new);
        y_smooth = pchip(t, y, t_new);
    catch
        % 备用方案：三次样条插值
        x_smooth = spline(t, x, t_new);
        y_smooth = spline(t, y, t_new);
    end

    % 组合结果并进行安全检查
    smooth_curve = [x_smooth(1), y_smooth(1)];

    for i = 2:length(x_smooth)
        candidate = [x_smooth(i), y_smooth(i)];

        % 检查是否与前一点距离合适且无碰撞
        if norm(candidate - smooth_curve(end, :)) >= 0.1  % 更小的最小间距，生成更细腻的曲线
            if ~check_collision_smooth(smooth_curve(end, :), candidate, binary_img)
                smooth_curve = [smooth_curve; candidate];
            end
        end
    end

    % 确保终点连接
    end_point = key_points(end, :);
    if norm(smooth_curve(end, :) - end_point) > 1.0
        if ~check_collision_smooth(smooth_curve(end, :), end_point, binary_img)
            smooth_curve = [smooth_curve; end_point];
        end
    end
end

%% 使用MATLAB内置smooth函数和SG滤波的平滑算法
function smoothed_path = advanced_cubic_smoothing(path, binary_img)
    if size(path, 1) <= 2
        smoothed_path = path;
        return;
    end

    fprintf('开始MATLAB内置平滑处理...\n');

    % 第一步：贪婪算法适度简化
    key_points = simple_greedy_simplification(path, binary_img);
    fprintf('贪婪简化: %d -> %d 个点\n', size(path, 1), size(key_points, 1));

    if size(key_points, 1) <= 5
        smoothed_path = key_points;
        return;
    end

    % 第二步：使用Savitzky-Golay滤波平滑
    sg_smoothed = savitzky_golay_smoothing(key_points, binary_img);
    fprintf('SG滤波平滑: %d -> %d 个点\n', size(key_points, 1), size(sg_smoothed, 1));

    % 第三步：使用MATLAB smooth函数进一步平滑
    final_smoothed = matlab_smooth_filter(sg_smoothed, binary_img);
    fprintf('MATLAB smooth平滑: %d -> %d 个点\n', size(sg_smoothed, 1), size(final_smoothed, 1));

    fprintf('MATLAB内置平滑完成！\n');
    smoothed_path = final_smoothed;
end

% 优化的贪婪算法 - 寻找更优路径并保留关键转折点
function key_points = simple_greedy_simplification(path, binary_img)
    if size(path, 1) <= 3
        key_points = path;
        return;
    end

    % 第一步：激进的贪婪简化，获得最优路径
    optimal_points = aggressive_greedy_optimization(path, binary_img);

    % 第二步：在关键转折点添加中间点，为平滑做准备
    key_points = add_smoothing_points(optimal_points, binary_img);
end

% 激进的贪婪优化 - 寻找最短路径
function optimal_points = aggressive_greedy_optimization(path, binary_img)
    optimal_points = path(1, :); % 起点
    current_idx = 1;
    n = size(path, 1);

    while current_idx < n
        % 寻找最远可达点（更激进）
        farthest_idx = current_idx + 1;

        for test_idx = current_idx + 2:n
            if ~check_collision_smooth(path(current_idx, :), path(test_idx, :), binary_img)
                farthest_idx = test_idx;
            else
                break; % 一旦碰撞就停止
            end
        end

        optimal_points = [optimal_points; path(farthest_idx, :)];
        current_idx = farthest_idx;
    end

    % 确保终点
    if norm(optimal_points(end, :) - path(end, :)) > 5
        optimal_points = [optimal_points; path(end, :)];
    end
end

% 在转折点添加平滑辅助点
function smoothing_points = add_smoothing_points(optimal_points, binary_img)
    if size(optimal_points, 1) <= 3
        smoothing_points = optimal_points;
        return;
    end

    smoothing_points = [];
    n = size(optimal_points, 1);

    for i = 1:n-1
        % 添加当前点
        smoothing_points = [smoothing_points; optimal_points(i, :)];

        if i < n-1  % 不是最后一段
            % 计算角度变化
            if i > 1
                v1 = optimal_points(i, :) - optimal_points(i-1, :);
                v2 = optimal_points(i+1, :) - optimal_points(i, :);

                if norm(v1) > 0 && norm(v2) > 0
                    v1_norm = v1 / norm(v1);
                    v2_norm = v2 / norm(v2);

                    cos_angle = dot(v1_norm, v2_norm);
                    cos_angle = max(-1, min(1, cos_angle));
                    angle_change = acos(cos_angle);

                    % 如果角度变化大，添加辅助点用于平滑
                    if angle_change > pi / 8  % 22.5度
                        % 在转折点前后添加辅助点
                        segment_length = norm(optimal_points(i+1, :) - optimal_points(i, :));
                        if segment_length > 20  % 只在长段上添加
                            aux_point = optimal_points(i, :) + 0.3 * (optimal_points(i+1, :) - optimal_points(i, :));
                            if ~check_collision_smooth(optimal_points(i, :), aux_point, binary_img)
                                smoothing_points = [smoothing_points; aux_point];
                            end
                        end
                    end
                end
            end
        end
    end

    % 添加终点
    smoothing_points = [smoothing_points; optimal_points(end, :)];
end

% 强力拐点平滑 - 专门处理尖锐拐点
function sg_smoothed = savitzky_golay_smoothing(key_points, binary_img)
    if size(key_points, 1) <= 5
        sg_smoothed = key_points;
        return;
    end

    % 第一步：使用三次样条插值增加点密度
    dense_points = increase_point_density(key_points, binary_img);

    % 第二步：应用强力平滑
    sg_smoothed = apply_strong_smoothing(dense_points, binary_img);
end

% 增加点密度 - 为平滑提供更多控制点
function dense_points = increase_point_density(key_points, binary_img)
    if size(key_points, 1) <= 3
        dense_points = key_points;
        return;
    end

    % 使用三次样条插值增加点密度
    x = key_points(:, 1);
    y = key_points(:, 2);

    % 计算弧长参数
    t = zeros(length(x), 1);
    for i = 2:length(t)
        t(i) = t(i-1) + norm(key_points(i, :) - key_points(i-1, :));
    end

    if t(end) == 0
        dense_points = key_points;
        return;
    end

    t = t / t(end);

    % 增加点密度 - 每2像素一个点
    total_length = sum(sqrt(diff(x).^2 + diff(y).^2));
    num_points = max(20, ceil(total_length / 2.0));

    t_new = linspace(0, 1, num_points);

    % 使用PCHIP插值
    try
        x_new = pchip(t, x, t_new);
        y_new = pchip(t, y, t_new);
    catch
        x_new = interp1(t, x, t_new, 'linear');
        y_new = interp1(t, y, t_new, 'linear');
    end

    % 碰撞检测
    dense_points = [x_new(1), y_new(1)];
    for i = 2:length(x_new)
        candidate = [x_new(i), y_new(i)];
        if ~check_collision_smooth(dense_points(end, :), candidate, binary_img)
            dense_points = [dense_points; candidate];
        end
    end
end

% 应用强力平滑
function smoothed = apply_strong_smoothing(dense_points, binary_img)
    if size(dense_points, 1) <= 5
        smoothed = dense_points;
        return;
    end

    x = dense_points(:, 1);
    y = dense_points(:, 2);
    n = length(x);

    % 多次平滑处理
    for iteration = 1:3  % 进行3次平滑
        x_new = x;
        y_new = y;

        % 对每个内部点进行平滑
        for i = 3:n-2
            % 使用5点加权平均，权重向中心倾斜
            weights = [0.05, 0.25, 0.4, 0.25, 0.05];

            x_smooth = 0;
            y_smooth = 0;
            for j = -2:2
                x_smooth = x_smooth + weights(j+3) * x(i+j);
                y_smooth = y_smooth + weights(j+3) * y(i+j);
            end

            candidate = [x_smooth, y_smooth];

            % 检查平滑后的点是否安全
            if ~check_collision_smooth([x(i-1), y(i-1)], candidate, binary_img) && ...
               ~check_collision_smooth(candidate, [x(i+1), y(i+1)], binary_img)
                x_new(i) = x_smooth;
                y_new(i) = y_smooth;
            end
        end

        x = x_new;
        y = y_new;
    end

    smoothed = [x, y];
end

% 超强拐点平滑处理
function final_smoothed = matlab_smooth_filter(sg_path, binary_img)
    if size(sg_path, 1) <= 3
        final_smoothed = sg_path;
        return;
    end

    % 第一步：识别尖锐拐点
    sharp_corners = identify_sharp_corners(sg_path);

    % 第二步：对尖锐拐点进行特殊处理
    corner_smoothed = smooth_sharp_corners(sg_path, sharp_corners, binary_img);

    % 第三步：全局平滑
    final_smoothed = global_smoothing(corner_smoothed, binary_img);

    % 第四步：控制点密度
    final_smoothed = control_point_density(final_smoothed);
end

% 识别尖锐拐点
function sharp_corners = identify_sharp_corners(path)
    n = size(path, 1);
    sharp_corners = [];

    if n <= 3
        return;
    end

    for i = 2:n-1
        v1 = path(i, :) - path(i-1, :);
        v2 = path(i+1, :) - path(i, :);

        if norm(v1) > 0 && norm(v2) > 0
            v1_norm = v1 / norm(v1);
            v2_norm = v2 / norm(v2);

            cos_angle = dot(v1_norm, v2_norm);
            cos_angle = max(-1, min(1, cos_angle));
            angle_change = acos(cos_angle);

            % 角度变化大于30度认为是尖锐拐点
            if angle_change > pi / 6
                sharp_corners = [sharp_corners; i];
            end
        end
    end
end

% 平滑尖锐拐点
function smoothed_path = smooth_sharp_corners(path, sharp_corners, binary_img)
    smoothed_path = path;

    for i = 1:length(sharp_corners)
        corner_idx = sharp_corners(i);

        if corner_idx > 2 && corner_idx < size(path, 1) - 1
            % 使用更大范围的点进行平滑
            start_idx = max(1, corner_idx - 2);
            end_idx = min(size(path, 1), corner_idx + 2);

            % 提取局部路径段
            local_path = path(start_idx:end_idx, :);

            % 对局部路径段进行强力平滑
            smoothed_local = apply_corner_smoothing(local_path, binary_img);

            % 替换原路径中的对应段
            if size(smoothed_local, 1) >= 3
                mid_idx = ceil(size(smoothed_local, 1) / 2);
                smoothed_path(corner_idx, :) = smoothed_local(mid_idx, :);
            end
        end
    end
end

% 应用拐点平滑
function smoothed_local = apply_corner_smoothing(local_path, binary_img)
    if size(local_path, 1) <= 3
        smoothed_local = local_path;
        return;
    end

    % 使用三次样条插值增加点密度
    x = local_path(:, 1);
    y = local_path(:, 2);

    % 弧长参数化
    t = zeros(length(x), 1);
    for i = 2:length(t)
        t(i) = t(i-1) + norm(local_path(i, :) - local_path(i-1, :));
    end

    if t(end) == 0
        smoothed_local = local_path;
        return;
    end

    t = t / t(end);

    % 高密度插值
    num_points = length(x) * 3; % 增加3倍密度
    t_new = linspace(0, 1, num_points);

    % 使用三次样条插值
    try
        x_new = spline(t, x, t_new);
        y_new = spline(t, y, t_new);
    catch
        x_new = interp1(t, x, t_new, 'linear');
        y_new = interp1(t, y, t_new, 'linear');
    end

    smoothed_local = [x_new', y_new'];

    % 安全检查
    safe_local = smoothed_local(1, :);
    for i = 2:size(smoothed_local, 1)
        if ~check_collision_smooth(safe_local(end, :), smoothed_local(i, :), binary_img)
            safe_local = [safe_local; smoothed_local(i, :)];
        end
    end

    smoothed_local = safe_local;
end

% 全局平滑
function global_smoothed = global_smoothing(path, binary_img)
    if size(path, 1) <= 5
        global_smoothed = path;
        return;
    end

    x = path(:, 1);
    y = path(:, 2);

    % 多次迭代平滑
    for iter = 1:5  % 5次迭代
        x_new = x;
        y_new = y;

        % 对每个内部点进行平滑
        for i = 3:length(x)-2
            % 7点加权平均
            weights = [0.02, 0.08, 0.2, 0.4, 0.2, 0.08, 0.02];

            x_smooth = 0;
            y_smooth = 0;
            for j = -3:3
                x_smooth = x_smooth + weights(j+4) * x(i+j);
                y_smooth = y_smooth + weights(j+4) * y(i+j);
            end

            candidate = [x_smooth, y_smooth];

            % 安全检查
            if ~check_collision_smooth([x(i-1), y(i-1)], candidate, binary_img) && ...
               ~check_collision_smooth(candidate, [x(i+1), y(i+1)], binary_img)
                x_new(i) = x_smooth;
                y_new(i) = y_smooth;
            end
        end

        x = x_new;
        y = y_new;
    end

    global_smoothed = [x, y];
end

% 控制点密度
function controlled_path = control_point_density(path)
    if size(path, 1) <= 3
        controlled_path = path;
        return;
    end

    controlled_path = path(1, :); % 起点
    min_spacing = 1.5; % 最小间距

    for i = 2:size(path, 1)
        current_point = path(i, :);
        last_point = controlled_path(end, :);

        % 如果距离足够大，保留这个点
        if norm(current_point - last_point) >= min_spacing
            controlled_path = [controlled_path; current_point];
        end
    end

    % 确保终点
    end_point = path(end, :);
    if norm(controlled_path(end, :) - end_point) > 1.0
        controlled_path = [controlled_path; end_point];
    end
end







% PCHIP保形三次插值最终平滑
function final_path = pchip_final_smoothing(path_nodes, binary_img)
    if size(path_nodes, 1) <= 3
        final_path = path_nodes;
        return;
    end

    % 计算弧长参数
    x = path_nodes(:, 1);
    y = path_nodes(:, 2);

    t = zeros(length(x), 1);
    for i = 2:length(t)
        t(i) = t(i-1) + norm(path_nodes(i, :) - path_nodes(i-1, :));
    end

    if t(end) == 0
        final_path = path_nodes;
        return;
    end

    t = t / t(end); % 归一化

    % 计算插值密度
    total_length = sum(sqrt(diff(x).^2 + diff(y).^2));
    num_points = max(50, ceil(total_length / 0.8)); % 每0.8像素一个点

    t_new = linspace(0, 1, num_points);

    % PCHIP插值 - 保形性最好的三次插值
    try
        x_new = pchip(t, x, t_new);
        y_new = pchip(t, y, t_new);
    catch
        % 备用：三次样条
        x_new = spline(t, x, t_new);
        y_new = spline(t, y, t_new);
    end

    % 安全验证和密度控制
    final_path = [x_new(1), y_new(1)];
    min_spacing = 0.6; % 最小间距

    for i = 2:length(x_new)
        candidate = [x_new(i), y_new(i)];

        % 检查间距和碰撞
        if norm(candidate - final_path(end, :)) >= min_spacing
            if ~check_collision_smooth(final_path(end, :), candidate, binary_img)
                final_path = [final_path; candidate];
            end
        end
    end

    % 确保终点连接
    end_point = path_nodes(end, :);
    if norm(final_path(end, :) - end_point) > 1.0
        if ~check_collision_smooth(final_path(end, :), end_point, binary_img)
            final_path = [final_path; end_point];
        end
    end
end

% 安全碰撞检测 - 增加更大的安全边距
function collision = check_collision_smooth(node1, node2, binary_img)
    [height, width] = size(binary_img);

    distance = norm(node2 - node1);
    num_samples = max(ceil(distance * 3), 30); % 更密集采样

    for i = 0:num_samples
        t = i / num_samples;
        point = node1 + t * (node2 - node1);
        x = round(point(1));
        y = round(point(2));

        % 更严格的边界检查 - 增加安全边距
        safety_margin = 8; % 8像素安全边距
        if x < safety_margin || x > width-safety_margin || y < safety_margin || y > height-safety_margin
            collision = true;
            return;
        end

        % 检查更大的安全区域 - 7x7区域
        for dx = -3:3
            for dy = -3:3
                check_x = x + dx;
                check_y = y + dy;
                if check_x >= 1 && check_x <= width && check_y >= 1 && check_y <= height
                    if binary_img(check_y, check_x) == 1
                        collision = true;
                        return;
                    end
                end
            end
        end
    end
    collision = false;
end

%% 贪婪路径优化函数 - 关键改进
function optimized_path = optimize_path_greedy(path_nodes, binary_img)
    if size(path_nodes, 1) <= 2
        optimized_path = path_nodes;
        return;
    end

    fprintf('开始贪婪路径优化...\n');

    % 第一步：激进的贪婪优化，寻找最短路径
    optimized_path = aggressive_greedy_optimization_v2(path_nodes, binary_img);
    fprintf('贪婪优化: %d -> %d 个点\n', size(path_nodes, 1), size(optimized_path, 1));

    % 第二步：局部优化，进一步减少路径长度
    optimized_path = local_path_optimization(optimized_path, binary_img);
    fprintf('局部优化: %d 个点\n', size(optimized_path, 1));

    fprintf('贪婪路径优化完成！\n');
end

% 激进的贪婪优化 - 版本2，更加激进
function optimal_points = aggressive_greedy_optimization_v2(path, binary_img)
    optimal_points = path(1, :); % 起点
    current_idx = 1;
    n = size(path, 1);

    while current_idx < n
        % 寻找最远可达点（更激进的策略）
        farthest_idx = current_idx + 1;

        % 从最远的点开始向后搜索，找到第一个可达的点
        for test_idx = n:-1:(current_idx + 2)
            if ~check_collision_smooth(path(current_idx, :), path(test_idx, :), binary_img)
                farthest_idx = test_idx;
                break; % 找到最远可达点就停止
            end
        end

        % 如果没找到更远的点，就选择下一个点
        if farthest_idx == current_idx + 1
            for test_idx = current_idx + 2:n
                if ~check_collision_smooth(path(current_idx, :), path(test_idx, :), binary_img)
                    farthest_idx = test_idx;
                else
                    break; % 一旦碰撞就停止
                end
            end
        end

        optimal_points = [optimal_points; path(farthest_idx, :)];
        current_idx = farthest_idx;
    end

    % 确保终点
    if norm(optimal_points(end, :) - path(end, :)) > 5
        optimal_points = [optimal_points; path(end, :)];
    end
end

% 局部路径优化
function optimized_path = local_path_optimization(path_nodes, binary_img)
    if size(path_nodes, 1) <= 3
        optimized_path = path_nodes;
        return;
    end

    optimized_path = path_nodes;
    improved = true;
    iteration = 0;
    max_iterations = 5;

    % 迭代优化，直到没有改进或达到最大迭代次数
    while improved && iteration < max_iterations
        improved = false;
        iteration = iteration + 1;
        new_path = optimized_path(1, :); % 起点

        i = 1;
        while i < size(optimized_path, 1)
            % 尝试跳过更多的中间点
            best_next = i + 1;

            % 寻找最远的可直达点
            for j = min(i + 10, size(optimized_path, 1)):-1:(i + 2)
                if ~check_collision_smooth(optimized_path(i, :), optimized_path(j, :), binary_img)
                    best_next = j;
                    improved = true;
                    break;
                end
            end

            new_path = [new_path; optimized_path(best_next, :)];
            i = best_next;
        end

        optimized_path = new_path;
    end
end

%% 辅助函数
function [nearest_idx, nearest_node] = find_nearest(nodes, point)
    min_dist = inf;
    nearest_idx = 1;
    for i = 1:length(nodes)
        dist = norm(nodes(i).node - point);
        if dist < min_dist
            min_dist = dist;
            nearest_idx = i;
        end
    end
    nearest_node = nodes(nearest_idx).node;
end

function collision = check_collision(node1, node2, binary_img)
    [height, width] = size(binary_img);

    % 增加采样密度，确保不遗漏障碍物
    distance = norm(node2 - node1);
    num_samples = max(ceil(distance * 2), 50); % 更密集采样

    for i = 0:num_samples
        t = i / num_samples;
        point = node1 + t * (node2 - node1);
        x = round(point(1));
        y = round(point(2));

        % 更严格的边界检查
        if x < 10 || x > width-9 || y < 10 || y > height-9
            collision = true;
            return;
        end

        % 障碍物检查 - 检查周围5x5区域，增大安全边距
        for dx = -2:2
            for dy = -2:2
                check_x = x + dx;
                check_y = y + dy;
                if check_x >= 1 && check_x <= width && check_y >= 1 && check_y <= height
                    if binary_img(check_y, check_x) == 1
                        collision = true;
                        return;
                    end
                end
            end
        end
    end
    collision = false;
end

function [connected, start_idx, goal_idx] = check_connection(start_nodes, goal_nodes, tolerance, binary_img)
    connected = false;
    start_idx = -1;
    goal_idx = -1;
    min_dist = inf;

    for i = 1:length(start_nodes)
        for j = 1:length(goal_nodes)
            dist = norm(start_nodes(i).node - goal_nodes(j).node);
            if dist < tolerance && dist < min_dist
                if ~check_collision(start_nodes(i).node, goal_nodes(j).node, binary_img)
                    connected = true;
                    start_idx = i;
                    goal_idx = j;
                    min_dist = dist;
                end
            end
        end
    end
end

function path = build_path(start_nodes, goal_nodes, start_idx, goal_idx)
    % 从起点树回溯
    start_path = [];
    current = start_idx;
    while current ~= -1
        start_path = [start_nodes(current).node; start_path];
        current = start_nodes(current).parent;
    end

    % 从终点树回溯
    goal_path = [];
    current = goal_idx;
    while current ~= -1
        goal_path = [goal_path; goal_nodes(current).node];
        current = goal_nodes(current).parent;
    end

    % 合并路径
    path = [start_path; goal_path];
end

% RRT*扩展函数
function [nodes, new_node_idx] = extend_tree_rrt_star(nodes, random_point, step_size_min, step_size_max, binary_img, radius)
    new_node_idx = [];

    % 找到最近节点
    [nearest_idx, nearest_node] = find_nearest(nodes, random_point);

    % 计算新节点
    direction = (random_point - nearest_node);
    if norm(direction) > 0
        direction = direction / norm(direction);
        step_size = step_size_min + (step_size_max - step_size_min) * rand();
        new_node = nearest_node + direction * step_size;

        [height, width] = size(binary_img);
        if new_node(1) > 10 && new_node(1) < width-9 && new_node(2) > 10 && new_node(2) < height-9
            if ~check_collision(nearest_node, new_node, binary_img)
                % RRT*核心：寻找最优父节点
                min_cost = nodes(nearest_idx).cost + norm(new_node - nearest_node);
                best_parent = nearest_idx;

                % 在半径内寻找更好的父节点
                for i = 1:length(nodes)
                    if norm(nodes(i).node - new_node) < radius
                        potential_cost = nodes(i).cost + norm(new_node - nodes(i).node);
                        if potential_cost < min_cost && ~check_collision(nodes(i).node, new_node, binary_img)
                            min_cost = potential_cost;
                            best_parent = i;
                        end
                    end
                end

                % 添加新节点
                nodes(end+1) = struct('node', new_node, 'parent', best_parent, 'cost', min_cost);
                new_node_idx = length(nodes);

                % 重连优化：检查是否可以改善附近节点的路径
                for i = 1:length(nodes)-1
                    if norm(nodes(i).node - new_node) < radius
                        new_cost = min_cost + norm(nodes(i).node - new_node);
                        if new_cost < nodes(i).cost && ~check_collision(new_node, nodes(i).node, binary_img)
                            nodes(i).parent = new_node_idx;
                            nodes(i).cost = new_cost;
                        end
                    end
                end
            end
        end
    end
end
