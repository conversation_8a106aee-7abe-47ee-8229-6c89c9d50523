clc; clear; close all;

% 读取并处理图像
img = imread('3.png');
if size(img, 3) == 3
    gray_img = rgb2gray(img);
else
    gray_img = img;
end

% 二值化处理 - 创建清晰的黑白地图
binary_img = gray_img < 150; % 调整阈值
[height, width] = size(binary_img);

% 设置起点和终点
start_point = [100, height-100];  % 起点
goal_point = [width-100, 100];    % 终点

% 快速版本参数
max_iter = 1000;  % 减少迭代次数
step_size_min = 10;
step_size_max = 25;
goal_tolerance = 50;  % 增大连接容差
goal_bias = 0.3;  % 增加目标偏置
rewire_radius = 30;  % 减小重连半径

% 初始化树
start_nodes = struct('node', start_point, 'parent', -1, 'cost', 0);
goal_nodes = struct('node', goal_point, 'parent', -1, 'cost', 0);

fprintf('开始ADAPT-BiRRT*路径规划...\n');
tic;

% 主算法循环（简化版）
for iter = 1:max_iter
    % 简化的采样策略
    if rand() < goal_bias
        if mod(iter, 2) == 1
            random_point = goal_point;
        else
            random_point = start_point;
        end
    else
        random_point = [rand() * width, rand() * height];
    end
    
    % 交替扩展两棵树
    if mod(iter, 2) == 1
        [start_nodes, new_idx] = extend_tree_simple(start_nodes, random_point, step_size_min, step_size_max, binary_img);
    else
        [goal_nodes, new_idx] = extend_tree_simple(goal_nodes, random_point, step_size_min, step_size_max, binary_img);
    end
    
    % 每20次迭代检查连接
    if mod(iter, 20) == 0
        [connected, start_idx, goal_idx] = check_connection_simple(start_nodes, goal_nodes, goal_tolerance, binary_img);
        
        if connected
            path = build_path(start_nodes, goal_nodes, start_idx, goal_idx);
            % 关键：调用真正的平滑函数
            path = ultra_smooth_path(path, binary_img);
            fprintf('在第 %d 次迭代找到路径\n', iter);
            break;
        end
    end
    
    % 显示进度
    if mod(iter, 200) == 0
        fprintf('迭代进度: %d/%d\n', iter, max_iter);
    end
end

time_elapsed = toc;

% 创建论文展示图
figure('Position', [100, 100, 800, 600]);
imshow(~binary_img, 'InitialMagnification', 'fit');
hold on;

% 绘制起点和终点
plot(start_point(1), start_point(2), 'o', 'MarkerSize', 12, 'MarkerFaceColor', 'blue', 'MarkerEdgeColor', 'black', 'LineWidth', 2);
plot(goal_point(1), goal_point(2), 'o', 'MarkerSize', 12, 'MarkerFaceColor', 'red', 'MarkerEdgeColor', 'black', 'LineWidth', 2);

% 绘制路径 - 超平滑绿色曲线
if exist('path', 'var')
    for i = 1:size(path, 1)-1
        line([path(i,1), path(i+1,1)], [path(i,2), path(i+1,2)], ...
             'Color', 'green', 'LineWidth', 4);
    end
    
    % 计算路径长度
    path_length = 0;
    for i = 1:size(path, 1)-1
        path_length = path_length + norm(path(i+1,:) - path(i,:));
    end
    
    title('ADAPT-BiRRT* 超平滑路径规划', 'FontSize', 14, 'FontWeight', 'bold');
    
    fprintf('路径规划成功完成!\n');
    fprintf('路径长度: %.2f 像素\n', path_length);
    fprintf('计算时间: %.3f 秒\n', time_elapsed);
    fprintf('总节点数: %d\n', length(start_nodes) + length(goal_nodes));
    
    % 在图上添加统计信息
    text(10, height-30, sprintf('路径长度: %.1f 像素', path_length), ...
         'Color', 'black', 'FontSize', 10, 'BackgroundColor', 'white');
    text(10, height-50, sprintf('计算时间: %.2f 秒', time_elapsed), ...
         'Color', 'black', 'FontSize', 10, 'BackgroundColor', 'white');
else
    fprintf('在 %d 次迭代内未找到路径\n', max_iter);
    title('Path Planning Failed', 'FontSize', 14);
end

axis on;
xlabel('X (pixels)', 'FontSize', 12);
ylabel('Y (pixels)', 'FontSize', 12);

%% 核心函数

function [nearest_idx, nearest_node] = find_nearest(nodes, point)
    min_dist = inf;
    nearest_idx = 1;
    for i = 1:length(nodes)
        dist = norm(nodes(i).node - point);
        if dist < min_dist
            min_dist = dist;
            nearest_idx = i;
        end
    end
    nearest_node = nodes(nearest_idx).node;
end

% 快速碰撞检测函数
function collision = check_collision_fast(node1, node2, binary_img)
    [height, width] = size(binary_img);
    
    distance = norm(node2 - node1);
    num_samples = max(ceil(distance), 15);
    
    for i = 0:num_samples
        t = i / num_samples;
        point = node1 + t * (node2 - node1);
        x = round(point(1));
        y = round(point(2));
        
        if x < 8 || x > width-7 || y < 8 || y > height-7
            collision = true;
            return;
        end
        
        for dx = -2:2
            for dy = -2:2
                check_x = x + dx;
                check_y = y + dy;
                if check_x >= 1 && check_x <= width && check_y >= 1 && check_y <= height
                    if binary_img(check_y, check_x) == 1
                        collision = true;
                        return;
                    end
                end
            end
        end
    end
    collision = false;
end

% 简化的树扩展
function [nodes, new_node_idx] = extend_tree_simple(nodes, random_point, step_size_min, step_size_max, binary_img)
    new_node_idx = [];
    
    [nearest_idx, nearest_node] = find_nearest(nodes, random_point);
    
    direction = (random_point - nearest_node);
    if norm(direction) > 0
        direction = direction / norm(direction);
        step_size = step_size_min + (step_size_max - step_size_min) * rand();
        new_node = nearest_node + direction * step_size;
        
        [height, width] = size(binary_img);
        if new_node(1) > 10 && new_node(1) < width-9 && new_node(2) > 10 && new_node(2) < height-9
            if ~check_collision_fast(nearest_node, new_node, binary_img)
                cost = nodes(nearest_idx).cost + norm(new_node - nearest_node);
                nodes(end+1) = struct('node', new_node, 'parent', nearest_idx, 'cost', cost);
                new_node_idx = length(nodes);
            end
        end
    end
end

% 简化的连接检查
function [connected, start_idx, goal_idx] = check_connection_simple(start_nodes, goal_nodes, tolerance, binary_img)
    connected = false;
    start_idx = -1;
    goal_idx = -1;
    min_dist = inf;
    
    for i = 1:length(start_nodes)
        for j = 1:length(goal_nodes)
            dist = norm(start_nodes(i).node - goal_nodes(j).node);
            if dist < tolerance && dist < min_dist
                if ~check_collision_fast(start_nodes(i).node, goal_nodes(j).node, binary_img)
                    connected = true;
                    start_idx = i;
                    goal_idx = j;
                    min_dist = dist;
                end
            end
        end
    end
end

function path = build_path(start_nodes, goal_nodes, start_idx, goal_idx)
    % 从起点树回溯
    start_path = [];
    current = start_idx;
    while current ~= -1
        start_path = [start_nodes(current).node; start_path];
        current = start_nodes(current).parent;
    end
    
    % 从终点树回溯
    goal_path = [];
    current = goal_idx;
    while current ~= -1
        goal_path = [goal_path; goal_nodes(current).node];
        current = goal_nodes(current).parent;
    end
    
    % 合并路径
    path = [start_path; goal_path];
end

% 高级三次平滑路径函数 - 贪婪算法 + 多种三次插值技术
function smoothed_path = ultra_smooth_path(path, binary_img)
    if size(path, 1) <= 2
        smoothed_path = path;
        return;
    end

    fprintf('开始高级三次平滑处理...\n');

    % 第一步：贪婪算法优化 - 减少关键点
    key_points = greedy_path_simplification(path, binary_img);
    fprintf('贪婪算法优化: %d -> %d 个点\n', size(path, 1), size(key_points, 1));

    if size(key_points, 1) <= 3
        smoothed_path = key_points;
        return;
    end

    % 第二步：三次Hermite插值平滑
    hermite_path = cubic_hermite_interpolation(key_points, binary_img);
    fprintf('三次Hermite插值: %d -> %d 个点\n', size(key_points, 1), size(hermite_path, 1));

    % 第三步：三次贝塞尔曲线平滑
    bezier_path = cubic_bezier_smoothing(hermite_path, binary_img);
    fprintf('三次贝塞尔平滑: %d -> %d 个点\n', size(hermite_path, 1), size(bezier_path, 1));

    % 第四步：PCHIP保形三次插值最终优化
    final_path = pchip_final_smoothing(bezier_path, binary_img);
    fprintf('PCHIP最终平滑: %d -> %d 个点\n', size(bezier_path, 1), size(final_path, 1));

    smoothed_path = final_path;
    fprintf('高级三次平滑完成！最终路径: %d 个点\n', size(smoothed_path, 1));
end

% 贪婪算法路径简化
function simplified_path = greedy_path_simplification(path, binary_img)
    simplified_path = path(1, :); % 起点
    current_idx = 1;

    while current_idx < size(path, 1)
        % 寻找最远可达点
        farthest_idx = current_idx + 1;

        for test_idx = current_idx + 2:size(path, 1)
            if ~check_collision_smooth(path(current_idx, :), path(test_idx, :), binary_img)
                farthest_idx = test_idx;
            else
                break; % 一旦碰撞就停止
            end
        end

        simplified_path = [simplified_path; path(farthest_idx, :)];
        current_idx = farthest_idx;
    end
end

% 三次Hermite插值 - 保持切线连续性
function hermite_path = cubic_hermite_interpolation(key_points, binary_img)
    if size(key_points, 1) <= 3
        hermite_path = key_points;
        return;
    end

    n = size(key_points, 1);
    hermite_path = [];

    % 计算每个关键点的切线向量
    tangents = zeros(size(key_points));

    % 端点切线
    tangents(1, :) = key_points(2, :) - key_points(1, :);
    tangents(n, :) = key_points(n, :) - key_points(n-1, :);

    % 中间点切线 - 使用Catmull-Rom方法
    for i = 2:n-1
        tangents(i, :) = 0.5 * (key_points(i+1, :) - key_points(i-1, :));
    end

    % 归一化切线并控制长度
    for i = 1:n
        if norm(tangents(i, :)) > 0
            tangents(i, :) = tangents(i, :) / norm(tangents(i, :)) * 15;
        end
    end

    % 生成Hermite插值路径
    for i = 1:n-1
        p0 = key_points(i, :);
        p1 = key_points(i+1, :);
        m0 = tangents(i, :);
        m1 = tangents(i+1, :);

        % 计算插值点数
        segment_length = norm(p1 - p0);
        num_points = max(8, ceil(segment_length / 2));

        % Hermite插值
        for j = 0:num_points-1
            t = j / (num_points - 1);

            % Hermite基函数
            h00 = 2*t^3 - 3*t^2 + 1;
            h10 = t^3 - 2*t^2 + t;
            h01 = -2*t^3 + 3*t^2;
            h11 = t^3 - t^2;

            % 计算插值点
            point = h00*p0 + h10*m0 + h01*p1 + h11*m1;

            % 安全检查
            if isempty(hermite_path) || ~check_collision_smooth(hermite_path(end, :), point, binary_img)
                hermite_path = [hermite_path; point];
            end
        end
    end

    % 确保终点
    if ~isempty(hermite_path) && norm(hermite_path(end, :) - key_points(end, :)) > 2
        if ~check_collision_smooth(hermite_path(end, :), key_points(end, :), binary_img)
            hermite_path = [hermite_path; key_points(end, :)];
        end
    end
end

% 三次贝塞尔曲线平滑
function bezier_path = cubic_bezier_smoothing(path_nodes, binary_img)
    if size(path_nodes, 1) <= 3
        bezier_path = path_nodes;
        return;
    end

    bezier_path = [];
    n = size(path_nodes, 1);

    for i = 1:n-1
        p0 = path_nodes(i, :);
        p3 = path_nodes(i+1, :);

        % 计算控制点
        direction = p3 - p0;
        control_distance = norm(direction) / 3;

        if i == 1
            % 第一段
            if n > 2
                next_dir = path_nodes(i+2, :) - p0;
                next_dir = next_dir / norm(next_dir);
            else
                next_dir = direction / norm(direction);
            end
            p1 = p0 + next_dir * control_distance;
        else
            % 使用前一个点的方向
            prev_dir = p0 - path_nodes(i-1, :);
            prev_dir = prev_dir / norm(prev_dir);
            p1 = p0 + prev_dir * control_distance;
        end

        if i == n-1
            % 最后一段
            if i > 1
                prev_dir = p3 - path_nodes(i-1, :);
                prev_dir = prev_dir / norm(prev_dir);
                p2 = p3 - prev_dir * control_distance;
            else
                p2 = p3 - direction / norm(direction) * control_distance;
            end
        else
            % 使用下一个点的方向
            next_dir = path_nodes(i+2, :) - p0;
            next_dir = next_dir / norm(next_dir);
            p2 = p3 - next_dir * control_distance;
        end

        % 生成贝塞尔曲线点
        segment_length = norm(p3 - p0);
        num_points = max(10, ceil(segment_length / 1.5));

        for j = 0:num_points-1
            t = j / (num_points - 1);

            % 三次贝塞尔公式
            point = (1-t)^3 * p0 + 3*(1-t)^2*t * p1 + 3*(1-t)*t^2 * p2 + t^3 * p3;

            % 碰撞检测
            if isempty(bezier_path) || ~check_collision_smooth(bezier_path(end, :), point, binary_img)
                bezier_path = [bezier_path; point];
            end
        end
    end

    % 确保终点
    if ~isempty(bezier_path) && norm(bezier_path(end, :) - path_nodes(end, :)) > 2
        if ~check_collision_smooth(bezier_path(end, :), path_nodes(end, :), binary_img)
            bezier_path = [bezier_path; path_nodes(end, :)];
        end
    end
end

% PCHIP保形三次插值最终平滑
function final_path = pchip_final_smoothing(path_nodes, binary_img)
    if size(path_nodes, 1) <= 3
        final_path = path_nodes;
        return;
    end

    % 计算弧长参数
    x = path_nodes(:, 1);
    y = path_nodes(:, 2);

    t = zeros(length(x), 1);
    for i = 2:length(t)
        t(i) = t(i-1) + norm(path_nodes(i, :) - path_nodes(i-1, :));
    end

    if t(end) == 0
        final_path = path_nodes;
        return;
    end

    t = t / t(end); % 归一化

    % 计算插值密度
    total_length = sum(sqrt(diff(x).^2 + diff(y).^2));
    num_points = max(50, ceil(total_length / 0.8)); % 每0.8像素一个点

    t_new = linspace(0, 1, num_points);

    % PCHIP插值 - 保形性最好的三次插值
    try
        x_new = pchip(t, x, t_new);
        y_new = pchip(t, y, t_new);
    catch
        % 备用：三次样条
        x_new = spline(t, x, t_new);
        y_new = spline(t, y, t_new);
    end

    % 安全验证和密度控制
    final_path = [x_new(1), y_new(1)];
    min_spacing = 0.6; % 最小间距

    for i = 2:length(x_new)
        candidate = [x_new(i), y_new(i)];

        % 检查间距和碰撞
        if norm(candidate - final_path(end, :)) >= min_spacing
            if ~check_collision_smooth(final_path(end, :), candidate, binary_img)
                final_path = [final_path; candidate];
            end
        end
    end

    % 确保终点连接
    end_point = path_nodes(end, :);
    if norm(final_path(end, :) - end_point) > 1.0
        if ~check_collision_smooth(final_path(end, :), end_point, binary_img)
            final_path = [final_path; end_point];
        end
    end
end

% 平滑专用碰撞检测 - 更宽松但安全
function collision = check_collision_smooth(node1, node2, binary_img)
    [height, width] = size(binary_img);

    distance = norm(node2 - node1);
    num_samples = max(ceil(distance * 2), 25); % 适中密度

    for i = 0:num_samples
        t = i / num_samples;
        point = node1 + t * (node2 - node1);
        x = round(point(1));
        y = round(point(2));

        % 边界检查
        if x < 5 || x > width-4 || y < 5 || y > height-4
            collision = true;
            return;
        end

        % 检查3x3区域
        for dx = -1:1
            for dy = -1:1
                check_x = x + dx;
                check_y = y + dy;
                if check_x >= 1 && check_x <= width && check_y >= 1 && check_y <= height
                    if binary_img(check_y, check_x) == 1
                        collision = true;
                        return;
                    end
                end
            end
        end
    end
    collision = false;
end




