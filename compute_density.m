function density = compute_density(pos, obstacles, r, num_samples)
% 计算当前节点周围半径r范围内的障碍物密度
% 输入参数：
%   pos - 当前节点坐标 [x, y]
%   obstacles - 障碍物列表
%   r - 密度计算半径
%   num_samples - 采样点数
% 输出：
%   density - 障碍物密度（0到1之间）

% 筛选可能影响当前区域的障碍物
candidate_obs = [];
for j = 1:size(obstacles, 1)
    obs = obstacles(j, :);
    if obs(5) == 0  % 矩形
        obs_min = obs(1:2);
        obs_max = obs_min + obs(3:4);
        [dist, ~] = point2box(pos, obs_min, obs_max, obs);
    else            % 圆形
        center = obs(1:2);
        radius = obs(3);
        dist = max(norm(pos - center) - radius, 0);
    end
    if dist <= r
        candidate_obs = [candidate_obs; obs];
    end
end

% 无候选障碍物时密度为0
if isempty(candidate_obs)
    density = 0;
    return;
end

% 生成采样点
theta = 2 * pi * rand(num_samples, 1);
radius_samples = r * sqrt(rand(num_samples, 1));
x = pos(1) + radius_samples .* cos(theta);
y = pos(2) + radius_samples .* sin(theta);

% 统计落在障碍物内的点数
count = 0;
for i = 1:num_samples
    point = [x(i), y(i)];
    for j = 1:size(candidate_obs, 1)
        obs = candidate_obs(j, :);
        if obs(5) == 0  % 矩形
            x_min = obs(1); y_min = obs(2);
            x_max = x_min + obs(3); y_max = y_min + obs(4);
            if point(1) >= x_min && point(1) <= x_max && ...
               point(2) >= y_min && point(2) <= y_max
                count = count + 1;
                break;
            end
        else            % 圆形
            center = obs(1:2); radius = obs(3);
            if norm(point - center) <= radius
                count = count + 1;
                break;
            end
        end
    end
end
density = count / num_samples;
end

function [dist, proj_point] = point2circle(point, center, radius)
% 计算点到圆形障碍物的最近距离和投影点
vec = point - center;
dist_to_center = norm(vec);
if dist_to_center <= radius
    dist = 0;
    proj_point = point;  % 点在圆内，最近点为自身
else
    dist = dist_to_center - radius;
    proj_point = center + (vec / dist_to_center) * radius;
end
end

function [dist, proj_point] = point2box(point, box_min, box_max, obs)
% 计算点到矩形障碍物的最近距离和投影点（已适配圆形调用）
if obs(5) == 1  % 适配原point2box中的圆形处理
    center = obs(1:2);
    radius = obs(3);
    [dist, proj_point] = point2circle(point, center, radius);
    return;
end

% 处理矩形
proj_x = max(box_min(1), min(point(1), box_max(1)));
proj_y = max(box_min(2), min(point(2), box_max(2)));
proj_point = [proj_x, proj_y];
dist = norm(point - proj_point);
end